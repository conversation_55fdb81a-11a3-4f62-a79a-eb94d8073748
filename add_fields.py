import pyodbc

# 使用与app.py相同的连接方式
DRIVERS = [
    '{ODBC Driver 17 for SQL Server}',
    '{ODBC Driver 13 for SQL Server}',
    '{SQL Server Native Client 11.0}',
    '{SQL Server}',
]

def get_db_connection():
    """获取数据库连接 - Windows认证"""
    for driver in DRIVERS:
        try:
            connection_string = f"""
                DRIVER={driver};
                SERVER=localhost;
                DATABASE=DeviceDataSource;
                Trusted_Connection=yes;
                TrustServerCertificate=yes;
            """
            connection = pyodbc.connect(connection_string)
            return connection
        except Exception as e:
            print(f"尝试驱动 {driver} 失败: {e}")
            continue
    return None

try:
    conn = get_db_connection()
    if not conn:
        print("无法连接数据库")
        exit(1)
    
    cursor = conn.cursor()
    print("✅ 数据库连接成功")
    
    # 添加 devtype 字段
    try:
        cursor.execute("ALTER TABLE devinfotable ADD devtype nvarchar(50) NULL")
        print("✅ 已添加 devtype 字段")
    except Exception as e:
        if "已存在" in str(e) or "already exists" in str(e):
            print("ℹ️  devtype 字段已存在")
        else:
            print(f"添加 devtype 字段失败: {e}")
    
    # 添加 workspace 字段
    try:
        cursor.execute("ALTER TABLE devinfotable ADD workspace nvarchar(100) NULL")
        print("✅ 已添加 workspace 字段")
    except Exception as e:
        if "已存在" in str(e) or "already exists" in str(e):
            print("ℹ️  workspace 字段已存在")
        else:
            print(f"添加 workspace 字段失败: {e}")
    
    # 更新现有数据
    try:
        cursor.execute("""
            UPDATE devinfotable SET 
                devtype = CASE
                    WHEN devno LIKE 'WJ%' THEN N'五轴'
                    WHEN devno LIKE 'SC%' THEN N'数控'
                    WHEN devno LIKE 'JS%' THEN N'加工中心'
                    ELSE N'通用设备'
                END,
                workspace = CASE
                    WHEN devno IN ('WJ01', 'WJ02', 'WJ03', 'WJ04') THEN N'A区'      
                    WHEN devno IN ('SC01', 'SC02', 'SC03', 'SC04') THEN N'B区'      
                    WHEN devno IN ('SC05', 'SC06', 'SC07', 'SC08') THEN N'C区'      
                    ELSE N'未分配'
                END
            WHERE devtype IS NULL OR workspace IS NULL
        """)
        
        conn.commit()
        print("✅ 设备类型和生产位置信息已更新")
        
        # 查看更新结果
        cursor.execute('SELECT devno, devtype, workspace FROM devinfotable')
        rows = cursor.fetchall()
        print('\n📋 当前设备信息:')
        for row in rows:
            print(f'设备: {row[0]}, 类型: {row[1]}, 位置: {row[2]}')
            
    except Exception as e:
        print(f"更新数据失败: {e}")
    
    conn.close()
    print('\n🎉 数据库字段添加完成!')
    
except Exception as e:
    print(f'❌ 数据库操作失败: {e}')
