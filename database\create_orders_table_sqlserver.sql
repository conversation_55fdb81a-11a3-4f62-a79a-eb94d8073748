-- SQL Server 2008 数据库表创建脚本
-- 使用数据库
USE [DeviceDataSource]
GO

-- 为设备表添加新字段（如果表已存在）
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'DeviceInfo')
BEGIN
    -- 检查并添加设备类型字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('DeviceInfo') AND name = 'devtype')
    BEGIN
        ALTER TABLE [dbo].[DeviceInfo] ADD [devtype] [nvarchar](50) NULL;
        PRINT N'已添加设备类型字段 devtype';
    END

    -- 检查并添加生产位置字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('DeviceInfo') AND name = 'workspace')
    BEGIN
        ALTER TABLE [dbo].[DeviceInfo] ADD [workspace] [nvarchar](100) NULL;
        PRINT N'已添加生产位置字段 workspace';
    END

    -- 更新现有设备的类型和位置信息
    UPDATE [dbo].[DeviceInfo] SET
        [devtype] = CASE
            WHEN [devno] LIKE 'WJ%' THEN N'五轴'
            WHEN [devno] LIKE 'SC%' THEN N'数控'
            WHEN [devno] LIKE 'JS%' THEN N'加工中心'
            ELSE N'通用设备'
        END,
        [workspace] = CASE
            WHEN [devno] IN ('WJ01', 'WJ02', 'WJ03', 'WJ04') THEN N'A区'
            WHEN [devno] IN ('SC01', 'SC02', 'SC03', 'SC04') THEN N'B区'
            WHEN [devno] IN ('SC05', 'SC06', 'SC07', 'SC08') THEN N'C区'
            ELSE N'未分配'
        END
    WHERE [devtype] IS NULL OR [workspace] IS NULL;

    PRINT N'设备类型和生产位置信息已更新';
END
GO

-- 创建订单信息表
CREATE TABLE [dbo].[OrderInfo](
    [ID] [int] IDENTITY(1,1) NOT NULL,
    [OrderNumber] [nvarchar](50) NOT NULL,
    [ProductName] [nvarchar](200) NOT NULL,
    [CustomerName] [nvarchar](200) NOT NULL,
    [DeliveryDate] [datetime] NOT NULL,
    [ProductModel] [nvarchar](100) NULL,
    [Quantity] [int] NOT NULL DEFAULT 1,
    [UnitPrice] [decimal](10,2) NULL,
    [TotalAmount] [decimal](12,2) NULL,
    [OrderStatus] [nvarchar](20) NOT NULL DEFAULT 'pending',
    [Priority] [nvarchar](20) NOT NULL DEFAULT 'normal',
    [ContactPerson] [nvarchar](100) NULL,
    [ContactPhone] [nvarchar](20) NULL,
    [ContactEmail] [nvarchar](100) NULL,
    [DeliveryAddress] [nvarchar](500) NULL,
    [SpecialRequirements] [nvarchar](1000) NULL,
    [Notes] [nvarchar](1000) NULL,
    [CreatedBy] [nvarchar](100) NULL,
    [CreatedAt] [datetime] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime] NOT NULL DEFAULT GETDATE(),
    CONSTRAINT [PK_OrderInfo] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [UK_OrderInfo_OrderNumber] UNIQUE NONCLUSTERED ([OrderNumber] ASC)
)
GO

-- 创建索引
CREATE NONCLUSTERED INDEX [IX_OrderInfo_ProductName] ON [dbo].[OrderInfo]([ProductName] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_OrderInfo_CustomerName] ON [dbo].[OrderInfo]([CustomerName] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_OrderInfo_DeliveryDate] ON [dbo].[OrderInfo]([DeliveryDate] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_OrderInfo_OrderStatus] ON [dbo].[OrderInfo]([OrderStatus] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_OrderInfo_CreatedAt] ON [dbo].[OrderInfo]([CreatedAt] ASC)
GO

-- 创建更新时间触发器
CREATE TRIGGER [dbo].[TR_OrderInfo_UpdatedAt]
ON [dbo].[OrderInfo]
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE [dbo].[OrderInfo]
    SET [UpdatedAt] = GETDATE()
    WHERE [ID] IN (SELECT [ID] FROM inserted);
END
GO

-- 插入示例数据
INSERT INTO [dbo].[OrderInfo] (
    [OrderNumber], [ProductName], [CustomerName], [DeliveryDate], 
    [ProductModel], [Quantity], [UnitPrice], [TotalAmount], 
    [ContactPerson], [ContactPhone], [DeliveryAddress], [CreatedBy]
) VALUES 
('ORD20240118001', N'轨道制动盘', N'北京地铁集团', '2024-03-15', 'ZDP-350', 50, 2500.00, 125000.00, N'张经理', '13800138001', N'北京市朝阳区地铁总部大厦', 'admin'),
('ORD20240118002', N'制动闸片', N'上海申通地铁', '2024-03-20', 'ZZP-280', 100, 800.00, 80000.00, N'李主任', '13900139002', N'上海市徐汇区申通大厦', 'admin'),
('ORD20240118003', N'轮对总成', N'广州地铁运营', '2024-04-10', 'LDZ-1435', 20, 15000.00, 300000.00, N'王工程师', '13700137003', N'广州市天河区地铁大厦', 'admin'),
('ORD20240118004', N'牵引电机', N'深圳地铁集团', '2024-04-15', 'QYD-1200', 8, 45000.00, 360000.00, N'刘总监', '13600136004', N'深圳市福田区地铁大厦', 'admin'),
('ORD20240118005', N'转向架', N'成都地铁有限公司', '2024-05-01', 'ZXJ-B型', 12, 120000.00, 1440000.00, N'陈部长', '13500135005', N'成都市高新区地铁总部', 'admin');

-- 创建订单状态检查约束
ALTER TABLE [dbo].[OrderInfo]
ADD CONSTRAINT [CK_OrderInfo_OrderStatus] 
CHECK ([OrderStatus] IN ('pending', 'confirmed', 'production', 'completed', 'cancelled'));

-- 创建优先级检查约束
ALTER TABLE [dbo].[OrderInfo]
ADD CONSTRAINT [CK_OrderInfo_Priority] 
CHECK ([Priority] IN ('low', 'normal', 'high', 'urgent'));

-- 创建数量检查约束
ALTER TABLE [dbo].[OrderInfo]
ADD CONSTRAINT [CK_OrderInfo_Quantity] 
CHECK ([Quantity] > 0);

-- 创建单价检查约束
ALTER TABLE [dbo].[OrderInfo]
ADD CONSTRAINT [CK_OrderInfo_UnitPrice] 
CHECK ([UnitPrice] >= 0);

-- 创建总金额检查约束
ALTER TABLE [dbo].[OrderInfo]
ADD CONSTRAINT [CK_OrderInfo_TotalAmount] 
CHECK ([TotalAmount] >= 0);

PRINT N'订单信息表创建完成！';
