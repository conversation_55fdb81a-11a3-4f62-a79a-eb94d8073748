// 全局变量
let scene, camera, renderer, particles;
let stats = {
    running: 0,
    idle: 0,
    alarm: 0,
    offline: 0
};

// 当前选中的生产线ID
let currentLineId = 'all';

// 日历相关变量
let currentCalendarDate = new Date();
let alarmDates = new Set(); // 存储有异常的日期
let alarmChart = null; // 报警趋势图表

// API基础URL
const API_BASE_URL = ''; // 使用相对路径，因为前端和后端在同一服务器

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化...');
    initThreeJS();
    initDateTime();
    initWeather();
    initCalendar();
    initDataStatusIndicator();
    initRealTimeIndicator();
    initPageVisibilityHandler();
    loadProductionLines();
    initProductionLineSelector();
    initModalEvents();

    // 添加窗口大小变化监听器，用于图表自适应
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            // 重新渲染设备状态图表以适应新的窗口大小
            if (window.dataUpdateManager && typeof stats !== 'undefined' && stats) {
                window.dataUpdateManager.createEfficiencyChart(stats);
            }
            console.log('📊 窗口大小改变，重新渲染图表');
        }, 300); // 防抖，300ms后执行
    });

    // 延迟加载数据，确保所有库都已加载
    setTimeout(() => {
        loadAllData();
        startDataUpdate();
    }, 1000);
});

// 初始化页面可见性处理
function initPageVisibilityHandler() {
    let isPageVisible = !document.hidden;

    document.addEventListener('visibilitychange', function() {
        const wasVisible = isPageVisible;
        isPageVisible = !document.hidden;

        if (wasVisible && !isPageVisible) {
            // 页面变为不可见，暂停更新
            console.log('🔇 页面不可见，暂停实时更新');
            if (dataUpdateManager) {
                dataUpdateManager.pauseUpdates();
            }
            updateDataStatus('warning', '⏸️ 实时更新已暂停', '页面不可见时暂停更新以节省资源');
        } else if (!wasVisible && isPageVisible) {
            // 页面变为可见，恢复更新
            console.log('🔊 页面可见，恢复实时更新');
            if (dataUpdateManager) {
                dataUpdateManager.resumeUpdates();
            }
            updateDataStatus('success', '▶️ 实时更新已恢复', '页面可见时自动恢复数据更新');

            // 立即刷新一次数据
            setTimeout(() => {
                loadAllData().then(() => {
                    console.log('📊 页面恢复后数据刷新完成');
                });
            }, 500);
        }
    });

    console.log('👁️ 页面可见性监听器已初始化');
}

// 初始化数据状态指示器
function initDataStatusIndicator() {
    // 创建数据状态指示器
    const statusIndicator = document.createElement('div');
    statusIndicator.id = 'data-status-indicator';
    statusIndicator.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: #4fd1c7;
        padding: 8px 12px;
        border-radius: 5px;
        font-size: 12px;
        z-index: 1000;
        border: 1px solid #4fd1c7;
        display: none;
        max-width: 300px;
    `;
    statusIndicator.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <div id="status-icon" style="width: 8px; height: 8px; border-radius: 50%; background: #48bb78;"></div>
            <span id="status-text">数据连接正常</span>
        </div>
        <div id="status-details" style="font-size: 10px; color: #a0aec0; margin-top: 4px;"></div>
    `;

    document.body.appendChild(statusIndicator);

    // 5秒后自动隐藏
    setTimeout(() => {
        statusIndicator.style.display = 'none';
    }, 5000);
}

// 初始化实时状态指示器
function initRealTimeIndicator() {
    const realTimeIndicator = document.createElement('div');
    realTimeIndicator.id = 'realtime-indicator';
    realTimeIndicator.style.cssText = `
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(26, 35, 50, 0.95);
        color: #4fd1c7;
        padding: 8px 20px;
        font-size: 12px;
        z-index: 1001;
        border-top: 1px solid #4fd1c7;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 40px;
    `;

    realTimeIndicator.innerHTML = `
        <div style="display: flex; align-items: center; gap: 15px;">
            <div style="display: flex; align-items: center; gap: 8px;">
                <div id="realtime-pulse" style="width: 8px; height: 8px; border-radius: 50%; background: #48bb78; animation: pulse 2s infinite;"></div>
                <span style="font-weight: bold;">实时数据监控</span>
            </div>
            <div style="display: flex; align-items: center; gap: 20px; font-size: 11px; color: #a0aec0;">
                <span>设备状态: <span id="device-update-time" style="color: #4fd1c7; font-weight: bold;">--</span></span>
                <span>生产数据: <span id="production-update-time" style="color: #4fd1c7; font-weight: bold;">--</span></span>
                <span>系统状态: <span id="system-status" style="font-weight: bold;">正常</span></span>
            </div>
        </div>
        <div style="font-size: 11px; color: #a0aec0;">
            <span id="current-time-bottom"></span>
        </div>
    `;

    // 添加脉冲动画样式
    if (!document.querySelector('#pulse-animation')) {
        const pulseStyle = document.createElement('style');
        pulseStyle.id = 'pulse-animation';
        pulseStyle.textContent = `
            @keyframes pulse {
                0% { opacity: 1; transform: scale(1); }
                50% { opacity: 0.7; transform: scale(1.2); }
                100% { opacity: 1; transform: scale(1); }
            }
        `;
        document.head.appendChild(pulseStyle);
    }

    document.body.appendChild(realTimeIndicator);

    // 每秒更新时间显示
    setInterval(updateRealTimeIndicator, 1000);
}

// 更新实时指示器
function updateRealTimeIndicator() {
    const deviceUpdateEl = document.getElementById('device-update-time');
    const productionUpdateEl = document.getElementById('production-update-time');
    const systemStatusEl = document.getElementById('system-status');
    const currentTimeBottomEl = document.getElementById('current-time-bottom');

    if (!deviceUpdateEl || !productionUpdateEl || !systemStatusEl) return;

    const now = new Date();

    // 更新底部时间显示
    if (currentTimeBottomEl) {
        currentTimeBottomEl.textContent = now.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 更新设备状态时间（每3秒）
    const nextDeviceUpdate = 3 - (now.getSeconds() % 3);
    deviceUpdateEl.textContent = `${nextDeviceUpdate}s`;

    // 更新生产数据时间（每5秒）
    const nextProductionUpdate = 5 - (now.getSeconds() % 5);
    productionUpdateEl.textContent = `${nextProductionUpdate}s`;

    // 系统状态
    const totalErrors = dataUpdateManager ?
        Object.values(dataUpdateManager.updateStatus).reduce((sum, status) => sum + (status.errors || 0), 0) : 0;

    if (totalErrors === 0) {
        systemStatusEl.textContent = '正常';
        systemStatusEl.style.color = '#48bb78';
    } else if (totalErrors < 3) {
        systemStatusEl.textContent = '警告';
        systemStatusEl.style.color = '#ed8936';
    } else {
        systemStatusEl.textContent = '异常';
        systemStatusEl.style.color = '#f56565';
    }
}

// 更新数据状态
function updateDataStatus(status, message, details = '') {
    const indicator = document.getElementById('data-status-indicator');
    const icon = document.getElementById('status-icon');
    const text = document.getElementById('status-text');
    const detailsEl = document.getElementById('status-details');

    if (!indicator || !icon || !text || !detailsEl) return;

    // 显示指示器
    indicator.style.display = 'block';

    // 更新状态
    switch (status) {
        case 'success':
            icon.style.background = '#48bb78';
            text.textContent = message || '数据更新成功';
            break;
        case 'warning':
            icon.style.background = '#ed8936';
            text.textContent = message || '数据部分可用';
            break;
        case 'error':
            icon.style.background = '#f56565';
            text.textContent = message || '数据获取失败';
            break;
        case 'loading':
            icon.style.background = '#4fd1c7';
            text.textContent = message || '正在加载数据...';
            break;
    }

    detailsEl.textContent = details;

    // 2秒后自动隐藏（除非是错误状态）
    if (status !== 'error') {
        setTimeout(() => {
            indicator.style.display = 'none';
        }, 2000);
    }
}

// 初始化生产线选择器
function initProductionLineSelector() {
    const lineSelect = document.getElementById('production-line-select');
    const refreshBtn = document.getElementById('refresh-data-btn');

    // 生产线切换事件
    lineSelect.addEventListener('change', function() {
        currentLineId = this.value;
        console.log('切换到生产线:', currentLineId);
        loadAllData(); // 重新加载所有数据
    });

    // 刷新按钮事件
    refreshBtn.addEventListener('click', function() {
        console.log('🔄 手动刷新所有数据...');

        // 显示刷新状态
        updateDataStatus('loading', '正在刷新所有数据...', '请稍候');

        // 重置错误计数
        if (dataUpdateManager) {
            Object.keys(dataUpdateManager.updateStatus).forEach(key => {
                dataUpdateManager.updateStatus[key].errors = 0;
            });
        }

        // 执行完整数据刷新
        loadAllData().then(() => {
            updateDataStatus('success', '数据刷新完成', '所有数据已更新');
        }).catch(error => {
            console.error('手动刷新失败:', error);
            updateDataStatus('error', '数据刷新失败', error.message);
        });
    });
}

// 加载生产线列表
async function loadProductionLines() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/production-lines`);
        if (!response.ok) throw new Error('获取生产线列表失败');

        const lines = await response.json();
        const lineSelect = document.getElementById('production-line-select');

        // 清空现有选项
        lineSelect.innerHTML = '';

        // 添加生产线选项
        lines.forEach(line => {
            const option = document.createElement('option');
            option.value = line.lineid;
            option.textContent = line.linename;
            lineSelect.appendChild(option);
        });

        console.log('生产线列表加载完成:', lines);

    } catch (error) {
        console.error('加载生产线列表失败:', error);
        // 如果失败，保持默认的"全部生产线"选项
    }
}

// 加载设备统计
async function loadDeviceStats() {
    try {
        const url = currentLineId === 'all' ?
            `${API_BASE_URL}/api/device-stats` :
            `${API_BASE_URL}/api/device-stats/by-line/${currentLineId}`;

        console.log('加载设备统计，URL:', url);
        const response = await fetch(url);
        if (!response.ok) throw new Error('获取设备统计失败');

        stats = await response.json();
        updateStatsDisplay();
        console.log('设备统计加载完成:', stats);
    } catch (error) {
        console.error('加载设备统计失败:', error);
    }
}

// 更新统计显示
function updateStatsDisplay() {
    document.getElementById('running-count').textContent = stats.running || 0;
    document.getElementById('idle-count').textContent = stats.idle || 0;
    document.getElementById('alarm-count').textContent = stats.alarm || 0;
    document.getElementById('offline-count').textContent = stats.offline || 0;
}

// 加载设备列表
async function loadDevices() {
    try {
        const url = currentLineId === 'all' ?
            `${API_BASE_URL}/api/devices` :
            `${API_BASE_URL}/api/devices/by-line/${currentLineId}`;

        console.log('加载设备列表，URL:', url);
        const response = await fetch(url);
        if (!response.ok) throw new Error('获取设备列表失败');

        const devices = await response.json();
        renderDeviceCards(devices);
        console.log('设备列表加载完成:', devices.length + '台设备');
    } catch (error) {
        console.error('加载设备列表失败:', error);
        document.getElementById('device-grid').innerHTML = '<div class="error-card">设备数据加载失败</div>';
    }
}

// 渲染设备卡片
function renderDeviceCards(devices) {
    const deviceGrid = document.getElementById('device-grid');
    deviceGrid.innerHTML = '';

    if (!devices || devices.length === 0) {
        deviceGrid.innerHTML = '<div class="no-data">暂无设备数据</div>';
        return;
    }

    devices.forEach(device => {
        const card = document.createElement('div');
        card.className = `device-card-new status-${device.status}`;

        // 获取设备状态标签
        const statusBadge = getStatusBadge(device.status);

        // 使用真实的当日产量数据，不回退到partcount
        const productionCount = device.productionCount || 0;
        const efficiency = device.efficiency || 0;
        const oeeRate = device.devoee || 0;
        const utilizationRate = device.poweronrate || 0;

        // 获取执行程序信息，没有的话显示"未知"
        const executionProgram = device.exeprograme || device.executionProgram || '未知';

        // 格式化程序显示：程序：xxxx，如果太长则截断显示后面部分
        const formatProgramName = (program) => {
            if (program === '未知') return '程序：未知';
            if (program.length <= 12) return `程序：${program}`;
            // 如果太长，显示后面的部分
            return `程序：...${program.slice(-8)}`;
        };

        card.innerHTML = `
            <div class="device-header">
                <div class="device-id">${device.devno || device.id}</div>
                <div class="status-badge ${device.status}">${statusBadge}</div>
            </div>
            <div class="execution-rate ${executionProgram === '未知' ? 'unknown' : ''}" title="${executionProgram}">${formatProgramName(executionProgram)}</div>
            <div class="device-metrics">
                <div class="metric-row">
                    <div class="metric-item">
                        <div class="metric-value">${productionCount}</div>
                        <div class="metric-label">产量</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">${efficiency}%</div>
                        <div class="metric-label">效率</div>
                    </div>
                </div>
                <div class="metric-row">
                    <div class="metric-item">
                        <div class="metric-value">${oeeRate}%</div>
                        <div class="metric-label">设备OEE</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">${utilizationRate}%</div>
                        <div class="metric-label">开机率</div>
                    </div>
                </div>
            </div>
        `;

        card.addEventListener('click', () => showDeviceDetails(device));
        deviceGrid.appendChild(card);
    });

    // 添加状态指示器样式（如果还没有添加）
    if (!document.querySelector('#device-status-styles')) {
        addDeviceStatusStyles();
    }
}

// 添加设备状态样式
function addDeviceStatusStyles() {
    const style = document.createElement('style');
    style.id = 'device-status-styles';
    style.textContent = `
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-top: 10px;
            margin-left: auto;
            margin-right: auto;
        }
        .status-indicator.running { background: #48bb78; box-shadow: 0 0 10px #48bb78; }
        .status-indicator.idle { background: #ed8936; box-shadow: 0 0 10px #ed8936; }
        .status-indicator.alarm { background: #f56565; box-shadow: 0 0 10px #f56565; animation: blink 1s infinite; }
        .status-indicator.offline { background: #a0aec0; }
        .status-indicator.unknown { background: #4fd1c7; }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .status-text {
            font-weight: bold;
        }

        .error-card, .no-data {
            grid-column: 1 / -1;
            text-align: center;
            padding: 20px;
            color: #a0aec0;
            background: rgba(79, 209, 199, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(79, 209, 199, 0.3);
        }
    `;
    document.head.appendChild(style);
}

// 加载效率数据
async function loadEfficiencyData() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/efficiency`);
        if (!response.ok) throw new Error('获取效率数据失败');

        const efficiency = await response.json();

        // 初始化设备状态环形图
        if (window.dataUpdateManager && typeof stats !== 'undefined' && stats) {
            window.dataUpdateManager.createEfficiencyChart(stats);
        }
    } catch (error) {
        console.error('加载效率数据失败:', error);
    }
}

// 加载生产统计
async function loadProductionStats() {
    console.log('开始加载生产统计数据...');

    try {
        const url = `${API_BASE_URL}/api/production-stats`;
        console.log('请求URL:', url);

        const response = await fetch(url);
        console.log('API响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`获取生产统计失败: ${response.status} ${response.statusText}`);
        }

        const productionData = await response.json();
        console.log('生产统计原始数据:', productionData);

        // 更新生产统计显示 - 班次版本
        const statsContainer = document.getElementById('production-stats');
        console.log('找到统计容器:', statsContainer);

        if (!statsContainer) {
            console.error('未找到production-stats元素');
            return;
        }

        if (!productionData.devices || productionData.devices.length === 0) {
            console.log('没有设备数据，显示无数据提示');
            statsContainer.innerHTML = '<div class="no-data">暂无今日班次数据</div>';
            return;
        }

        console.log('设备数量:', productionData.devices.length);

        // 显示表头（移除日期标题）
        let html = `
            <div class="shift-header">
                <span class="device-col">设备名</span>
                <span class="shift-col">白班</span>
                <span class="shift-col">夜班</span>
                <span class="total-col">总数</span>
            </div>
        `;

        // 显示每个设备的班次数据
        productionData.devices.forEach((device, index) => {
            console.log(`处理设备${index + 1}:`, device);
            html += `
                <div class="device-shift-item">
                    <span class="device-name-shift">${device.devname}</span>
                    <span class="day-shift">${device.dayShift}</span>
                    <span class="night-shift">${device.nightShift}</span>
                    <span class="total-shift">${device.total}</span>
                </div>
            `;
        });

        // 移除汇总信息

        console.log('准备设置HTML内容，长度:', html.length);

        // 为内容添加滚动容器
        const needScroll = productionData.devices.length > 5; // 超过5个设备就开始滚动
        const scrollClass = needScroll ? 'production-stats-content' : 'production-stats-content';
        const scrollAttr = needScroll ? '' : 'data-scroll="false"';

        const finalHtml = `<div class="${scrollClass}" ${scrollAttr}>${html}</div>`;

        statsContainer.innerHTML = finalHtml;
        console.log('HTML内容已设置，滚动状态:', needScroll ? '启用' : '禁用');

        // 更新完成率（使用总产量的比例作为完成率）
        const completionRate = Math.min(100, Math.round((productionData.summary.totalProduction / 1000) * 100));
        console.log('计算完成率:', completionRate);
        updateCompletionRate(completionRate);

        console.log('生产统计加载完成');

    } catch (error) {
        console.error('加载生产统计失败 - 详细错误:', error);
        console.error('错误堆栈:', error.stack);

        const statsContainer = document.getElementById('production-stats');
        if (statsContainer) {
            statsContainer.innerHTML = `<div class="error">生产统计加载失败: ${error.message}</div>`;
        }
    }
}

// 更新完成率
function updateCompletionRate(rate) {
    console.log('尝试更新完成率:', rate);

    const completionRateEl = document.getElementById('completion-rate');
    if (completionRateEl) {
        completionRateEl.textContent = rate + '%';
        console.log('完成率文本已更新');
    } else {
        console.log('completion-rate元素不存在，跳过文本更新');
    }

    // 更新圆环进度
    const circle = document.querySelector('.rate-circle');
    if (circle) {
        const degree = (rate / 100) * 360;
        circle.style.background = `conic-gradient(#4fd1c7 0deg ${degree}deg, rgba(79, 209, 199, 0.2) ${degree}deg 360deg)`;
        console.log('完成率圆环已更新');
    } else {
        console.log('rate-circle元素不存在，跳过圆环更新');
    }
}

// 加载最新信息
async function loadLatestInfo() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/latest-info`);
        if (!response.ok) throw new Error('获取最新信息失败');

        const latestInfo = await response.json();
        renderLatestInfo(latestInfo);
    } catch (error) {
        console.error('加载最新信息失败:', error);
        document.getElementById('latest-info').innerHTML = '<div class="error">最新信息加载失败</div>';
    }
}

// 渲染最新信息
function renderLatestInfo(infos) {
    const latestInfoContainer = document.getElementById('latest-info');
    latestInfoContainer.innerHTML = '';

    if (!infos || infos.length === 0) {
        latestInfoContainer.innerHTML = '<div class="no-data">暂无最新信息</div>';
        return;
    }

    infos.forEach(info => {
        const item = document.createElement('div');
        item.className = 'info-item-inline';
        item.innerHTML = `
            <span class="info-time-inline">${info.time}</span>
            <span class="info-message-inline">${info.message}</span>
        `;
        latestInfoContainer.appendChild(item);
    });
}

// 加载趋势数据
async function loadTrendData() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/trend-data`);
        if (!response.ok) throw new Error('获取趋势数据失败');

        const trendData = await response.json();
        window.trendData = trendData; // 保存到全局变量供图表使用

        // 如果有错误信息，在控制台显示
        if (trendData.error) {
            console.warn('趋势数据获取警告:', trendData.error);
        }
        if (trendData.note) {
            console.log('趋势数据说明:', trendData.note);
        }
    } catch (error) {
        console.error('加载趋势数据失败:', error);
        // 使用空数据而不是模拟数据
        window.trendData = generateEmptyTrendData();
    }
}

// 生成空的趋势数据结构
function generateEmptyTrendData() {
    const labels = [];
    const now = new Date();

    for (let i = 6; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(now.getDate() - i);
        labels.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));
    }

    console.log('生成空趋势数据，标签:', labels);
    return {
        labels,
        data: [0, 0, 0, 0, 0, 0, 0], // 使用空数据
        note: '暂无数据'
    };
}

// 初始化Three.js背景
function initThreeJS() {
    const container = document.getElementById('three-bg');

    // 创建场景
    scene = new THREE.Scene();

    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 50;

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    container.appendChild(renderer.domElement);

    // 创建粒子系统
    createParticleSystem();

    // 创建网格背景
    createGridBackground();

    // 开始渲染循环
    animate();

    // 监听窗口大小变化
    window.addEventListener('resize', onWindowResize);
}

// 创建粒子系统
function createParticleSystem() {
    const particleCount = 1000;
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);

    const color = new THREE.Color();

    for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // 随机位置
        positions[i3] = (Math.random() - 0.5) * 200;
        positions[i3 + 1] = (Math.random() - 0.5) * 200;
        positions[i3 + 2] = (Math.random() - 0.5) * 200;

        // 渐变颜色
        color.setHSL(0.5 + Math.random() * 0.3, 0.7, 0.5 + Math.random() * 0.3);
        colors[i3] = color.r;
        colors[i3 + 1] = color.g;
        colors[i3 + 2] = color.b;
    }

    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const material = new THREE.PointsMaterial({
        size: 2,
        vertexColors: true,
        transparent: true,
        opacity: 0.6
    });

    particles = new THREE.Points(geometry, material);
    scene.add(particles);
}

// 创建网格背景
function createGridBackground() {
    const gridHelper = new THREE.GridHelper(200, 20, 0x4fd1c7, 0x2d3748);
    gridHelper.material.transparent = true;
    gridHelper.material.opacity = 0.3;
    scene.add(gridHelper);

    // 添加一些发光的立方体
    for (let i = 0; i < 10; i++) {
        const geometry = new THREE.BoxGeometry(2, 2, 2);
        const material = new THREE.MeshBasicMaterial({
            color: Math.random() > 0.5 ? 0x4fd1c7 : 0x48bb78,
            transparent: true,
            opacity: 0.7
        });

        const cube = new THREE.Mesh(geometry, material);
        cube.position.set(
            (Math.random() - 0.5) * 100,
            (Math.random() - 0.5) * 100,
            (Math.random() - 0.5) * 100
        );
        cube.rotation.set(
            Math.random() * Math.PI,
            Math.random() * Math.PI,
            Math.random() * Math.PI
        );

        scene.add(cube);
    }
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);

    // 旋转粒子系统
    if (particles) {
        particles.rotation.x += 0.001;
        particles.rotation.y += 0.002;
    }

    // 旋转场景中的立方体
    scene.children.forEach(child => {
        if (child.geometry && child.geometry.type === 'BoxGeometry') {
            child.rotation.x += 0.01;
            child.rotation.y += 0.01;
        }
    });

    renderer.render(scene, camera);
}

// 窗口大小变化处理
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// 初始化图表
function initCharts() {
    // 检查Chart.js是否已加载
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js 尚未加载，延迟初始化图表...');
        setTimeout(initCharts, 500);
        return;
    }

    // 七日产量趋势图
    const trendCtx = document.getElementById('trendChart');
    if (!trendCtx) {
        console.warn('未找到趋势图表元素 #trendChart');
        return;
    }

    console.log('找到图表元素，开始初始化图表...');
    const ctx = trendCtx.getContext('2d');
    const trendData = window.trendData || generateEmptyTrendData();
    console.log('图表数据:', trendData);

    // 销毁现有图表（如果存在）
    if (window.trendChart && typeof window.trendChart.destroy === 'function') {
        window.trendChart.destroy();
    }

    // 确定图表标题和数据状态
    let chartTitle = '日产量';
    let datasetLabel = '日产量';

    if (trendData.error) {
        chartTitle += ' (数据获取失败)';
        datasetLabel += ' (无数据)';
    } else if (trendData.note) {
        chartTitle += ` (${trendData.note})`;
    }

    // 检查是否有真实数据
    const hasRealData = trendData.data && trendData.data.some(value => value > 0);
    const borderColor = '#4fd1c7'; // 始终使用主题色
    const backgroundColor = 'rgba(79, 209, 199, 0.1)'; // 始终使用主题色

    window.trendChart = new Chart(ctx, {
        type: 'bar', // 改为柱状图
        data: {
            labels: trendData.labels || [],
            datasets: [{
                label: datasetLabel,
                data: trendData.data || [],
                borderColor: borderColor,
                backgroundColor: hasRealData ?
                    'rgba(79, 209, 199, 0.8)' : // 有数据时更明显的颜色
                    'rgba(79, 209, 199, 0.3)',  // 无数据时较淡的颜色
                borderWidth: 1,
                borderRadius: 6, // 圆角柱状图
                borderSkipped: false,
                barThickness: 'flex', // 自适应柱子宽度
                maxBarThickness: 40   // 最大柱子宽度
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    top: 10,
                    right: 10,
                    bottom: 5,
                    left: 10
                }
            },
            plugins: {
                title: {
                    display: false // 隐藏标题
                },
                legend: {
                    display: false // 隐藏图例
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (!hasRealData) {
                                return `日产量: 暂无数据`;
                            }
                            return `日产量: ${context.parsed.y.toLocaleString()} 件`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: false // 隐藏X轴标题
                    },
                    ticks: {
                        color: '#a0aec0',
                        font: {
                            size: 11 // 稍微小一点的字体
                        }
                    },
                    grid: {
                        display: false // 隐藏X轴网格线
                    }
                },
                y: {
                    title: {
                        display: false // 隐藏Y轴标题
                    },
                    beginAtZero: true,
                    ticks: {
                        color: '#a0aec0',
                        font: {
                            size: 10 // 更小的字体
                        },
                        callback: function(value) {
                            if (!hasRealData && value === 0) {
                                return '0';
                            }
                            // 简化数字显示
                            if (value >= 1000) {
                                return (value / 1000).toFixed(1) + 'k';
                            }
                            return value.toString();
                        }
                    },
                    grid: {
                        color: 'rgba(79, 209, 199, 0.1)',
                        lineWidth: 1
                    }
                }
            }
        }
    });

    console.log('趋势图表初始化完成', hasRealData ? '(有真实数据)' : '(无真实数据)');
    console.log('图表实例:', window.trendChart);
}

// 初始化日期时间
function initDateTime() {
    function updateDateTime() {
        const now = new Date();
        const dateStr = now.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
        const timeStr = now.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        // 检查元素是否存在再更新
        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time-display');

        if (dateElement) {
            dateElement.textContent = dateStr;
        }
        if (timeElement) {
            timeElement.textContent = timeStr;
        }
    }

    updateDateTime();
    setInterval(updateDateTime, 1000);
}

// 获取状态文字
function getStatusText(status) {
    const statusMap = {
        running: '运行中',
        idle: '空闲',
        alarm: '报警',
        offline: '离线',
        unknown: '未知'
    };
    return statusMap[status] || '未知';
}

// 获取状态标签
function getStatusBadge(status) {
    const statusMap = {
        running: '运行',
        idle: '空闲',
        alarm: '报警',
        offline: '离线',
        unknown: '未知'
    };
    return statusMap[status] || '未知';
}

// 格式化坐标显示
function formatCoordinateDisplay(coordinateStr) {
    if (!coordinateStr || coordinateStr === '未知' || coordinateStr.trim() === '') {
        return '<div class="no-data">暂无坐标数据</div>';
    }

    // 将坐标字符串按行分割并格式化
    const lines = coordinateStr.split('\n').filter(line => line.trim() !== '');
    if (lines.length === 0) {
        return '<div class="no-data">暂无坐标数据</div>';
    }

    return lines.map(line => `<div class="coordinate-line">${line.trim()}</div>`).join('');
}

// 格式化程序显示
function formatProgramDisplay(programStr) {
    if (!programStr || programStr === '未知' || programStr.trim() === '') {
        return '<div class="no-data">暂无程序数据</div>';
    }

    // 将程序内容按行分割并格式化，保留所有行（包括空行）
    const lines = programStr.split('\n');
    if (lines.length === 0) {
        return '<div class="no-data">暂无程序数据</div>';
    }

    // 为每一行添加行号并格式化显示
    return lines.map((line, index) => {
        const lineNumber = (index + 1).toString().padStart(3, '0');
        const lineContent = line.trim() || '&nbsp;'; // 空行显示为空格
        return `<div class="program-line"><span class="line-number">${lineNumber}:</span> ${lineContent}</div>`;
    }).join('');
}

// 显示设备详情
async function showDeviceDetails(device) {
    try {
        // 从API获取详细信息
        const response = await fetch(`${API_BASE_URL}/api/devices/${device.id}`);
        if (!response.ok) throw new Error('获取设备详情失败');

        const deviceDetail = await response.json();

        const modal = document.createElement('div');
        modal.className = 'device-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close-btn-simple" title="关闭详情">&times;</span>
                <div class="modal-header">
                    <h2>${deviceDetail.name} 详情</h2>
                    <div class="refresh-indicator" id="refreshIndicator">
                        <span class="refresh-dot"></span>
                        <span class="refresh-text">自动刷新中...</span>
                    </div>
                </div>
                <div class="device-details">
                    <div class="detail-section">
                        <h3>基本信息</h3>
                        <p><strong>设备ID:</strong> ${deviceDetail.id}</p>
                        <p><strong>设备类型:</strong> ${deviceDetail.type}</p>
                        <p><strong>位置:</strong> ${deviceDetail.location || '未知'}</p>
                        <p><strong>当前状态:</strong> <span class="status-${deviceDetail.status}">${getStatusText(deviceDetail.status)}</span></p>
                        ${deviceDetail.status === 'alarm' ? `<p><strong>最后报警:</strong> <span class="alarm-message" id="lastAlarmMessage">正在获取...</span></p>` : ''}
                        <p><strong>最后更新:</strong> ${new Date(deviceDetail.lastUpdate).toLocaleString('zh-CN')}</p>
                    </div>

                    <div class="detail-section">
                        <h3>生产信息</h3>
                        <p><strong>生产效率:</strong> ${deviceDetail.efficiency}%</p>
                        <p><strong>生产数量:</strong> ${deviceDetail.productionCount}</p>
                        <p><strong>总产量:</strong> ${deviceDetail.partcount || '未知'}</p>
                        <p><strong>加工模式:</strong> ${deviceDetail.devmodel || '未知'}</p>
                        <p><strong>程序:</strong> ${deviceDetail.exeprograme || '未知'}</p>
                        <p><strong>子程序:</strong> ${deviceDetail.subprogramName || '未知'}</p>
                        <p><strong>机床型号:</strong> ${deviceDetail.nctype || '未知'}</p>
                        <p><strong>执行行号:</strong> ${deviceDetail.programlinenumber || '未知'}</p>
                    </div>

                    <div class="detail-section">
                        <h3>加工参数</h3>
                        <p><strong>主轴转速:</strong> ${deviceDetail.spindlespeed || '未知'}</p>
                        <p><strong>进给速度:</strong> ${deviceDetail.feedrate || '未知'}</p>
                        <p><strong>主轴倍率:</strong> ${deviceDetail.spindleoverride || '未知'}</p>
                        <p><strong>进给倍率:</strong> ${deviceDetail.feedrateoverride || '未知'}</p>
                        <p><strong>快速移动倍率:</strong> ${deviceDetail.rapidoverride || '未知'}</p>
                        <p><strong>刀具号:</strong> ${deviceDetail.toolnumber || '未知'}</p>
                        <p><strong>主轴温度:</strong> ${deviceDetail.spindletemperature || '未知'}</p>
                        <p><strong>加工轴数:</strong> ${deviceDetail.axiscount || '未知'}</p>
                    </div>

                    <div class="detail-section">
                        <h3>运行时间</h3>
                        <p><strong>开机时间:</strong> ${deviceDetail.powerontime || '未知'}</p>
                        <p><strong>运行时间:</strong> ${deviceDetail.oprtime || '未知'}</p>
                        <p><strong>切削时间:</strong> ${deviceDetail.cuttime || '未知'}</p>
                        <p><strong>循环时间:</strong> ${deviceDetail.cyctime || '未知'}</p>
                    </div>

                    <div class="detail-section production-history-section">
                        <h3>过去七天加工产量</h3>
                        <div class="production-table">
                            <table id="productionTable">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>白班产量</th>
                                        <th>夜班产量</th>
                                        <th>日总产量</th>
                                    </tr>
                                </thead>
                                <tbody id="productionTableBody">
                                    <tr><td colspan="4">正在加载...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="detail-section coordinate-program-section">
                        <h3>坐标与程序信息</h3>
                        <div class="coordinate-program-grid">
                            <div class="coordinate-item">
                                <strong>相对坐标:</strong>
                                <div class="coordinate-content">${formatCoordinateDisplay(deviceDetail.relativeposition)}</div>
                            </div>
                            <div class="coordinate-item">
                                <strong>绝对坐标:</strong>
                                <div class="coordinate-content">${formatCoordinateDisplay(deviceDetail.absoluteposition)}</div>
                            </div>
                            <div class="coordinate-item">
                                <strong>机械坐标:</strong>
                                <div class="coordinate-content">${formatCoordinateDisplay(deviceDetail.machineposition)}</div>
                            </div>
                            <div class="program-item">
                                <strong>程序:</strong>
                                <div class="program-content">${formatProgramDisplay(deviceDetail.programactualblock)}</div>
                            </div>
                        </div>
                    </div>

                    ${deviceDetail.toolUseData && deviceDetail.toolUseData.length > 0 ? `
                        <div class="detail-section tool-use-section">
                            <h3>刀具使用统计</h3>
                            <div class="tool-use-table">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>刀具号</th>
                                            <th>使用次数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${deviceDetail.toolUseData.map(tool => `
                                            <tr>
                                                <td>${tool.toolNumber}</td>
                                                <td>${tool.useCount}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        // 添加模态框样式
        addModalStyles();

        document.body.appendChild(modal);

        // 存储刷新定时器ID
        let refreshTimer = null;

        // 刷新设备详情数据
        const refreshDeviceDetail = async () => {
            try {
                // 显示刷新动画
                const refreshIndicator = modal.querySelector('#refreshIndicator');
                if (refreshIndicator) {
                    refreshIndicator.style.opacity = '1';
                }

                const response = await fetch(`${API_BASE_URL}/api/devices/${device.id}`);
                if (!response.ok) return;

                const updatedDetail = await response.json();

                // 更新基本信息
                const statusElement = modal.querySelector('.status-' + deviceDetail.status);
                if (statusElement) {
                    statusElement.textContent = getStatusText(updatedDetail.status);
                    statusElement.className = `status-${updatedDetail.status}`;
                }

                // 更新生产数量
                const productionElements = modal.querySelectorAll('p');
                productionElements.forEach(p => {
                    if (p.innerHTML.includes('生产数量:')) {
                        p.innerHTML = `<strong>生产数量:</strong> ${updatedDetail.productionCount}`;
                    }
                    if (p.innerHTML.includes('生产效率:')) {
                        p.innerHTML = `<strong>生产效率:</strong> ${updatedDetail.efficiency}%`;
                    }
                    if (p.innerHTML.includes('最后更新:')) {
                        p.innerHTML = `<strong>最后更新:</strong> ${new Date(updatedDetail.lastUpdate).toLocaleString('zh-CN')}`;
                    }
                });

                // 更新坐标信息
                const coordinateContents = modal.querySelectorAll('.coordinate-content');
                if (coordinateContents.length >= 3) {
                    coordinateContents[0].innerHTML = formatCoordinateDisplay(updatedDetail.relativeposition);
                    coordinateContents[1].innerHTML = formatCoordinateDisplay(updatedDetail.absoluteposition);
                    coordinateContents[2].innerHTML = formatCoordinateDisplay(updatedDetail.machineposition);
                }

                // 更新程序内容
                const programContent = modal.querySelector('.program-content');
                if (programContent) {
                    programContent.innerHTML = formatProgramDisplay(updatedDetail.programactualblock);
                }

                // 淡出刷新指示器
                setTimeout(() => {
                    if (refreshIndicator) {
                        refreshIndicator.style.opacity = '0.6';
                    }
                }, 1000);

                console.log('设备详情已刷新');
            } catch (error) {
                console.error('刷新设备详情失败:', error);
                // 隐藏刷新指示器
                const refreshIndicator = modal.querySelector('#refreshIndicator');
                if (refreshIndicator) {
                    refreshIndicator.style.opacity = '0.3';
                }
            }
        };

        // 关闭模态框
        const closeModal = () => {
            if (refreshTimer) {
                clearInterval(refreshTimer);
                refreshTimer = null;
            }
            document.body.removeChild(modal);
        };

        modal.querySelector('.close-btn-simple').addEventListener('click', closeModal);

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });

        // 启动自动刷新（10秒间隔）
        refreshTimer = setInterval(refreshDeviceDetail, 10000);

        // 加载设备产量数据
        setTimeout(() => {
            loadDeviceProductionHistory(deviceDetail.id);
        }, 100);

        // 如果设备状态为报警，加载最后一条报警信息
        if (deviceDetail.status === 'alarm') {
            setTimeout(() => {
                loadLastAlarmMessage(deviceDetail.id);
            }, 200);
        }

    } catch (error) {
        console.error('获取设备详情失败:', error);
        alert('获取设备详情失败，请稍后再试');
    }
}

// 加载设备产量历史数据
async function loadDeviceProductionHistory(deviceId) {
    try {
        const response = await fetch(`${API_BASE_URL}/api/devices/${deviceId}/production-history`);
        if (!response.ok) throw new Error('获取产量历史失败');

        const productionData = await response.json();
        renderProductionTable(productionData);
    } catch (error) {
        console.error('加载产量历史失败:', error);
        const tableBody = document.getElementById('productionTableBody');
        if (tableBody) {
            tableBody.innerHTML = '<tr><td colspan="4">暂无产量数据</td></tr>';
        }
    }
}

// 渲染产量表格
function renderProductionTable(productionData) {
    const tableBody = document.getElementById('productionTableBody');
    if (!tableBody) return;

    if (!productionData || productionData.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="4">暂无产量数据</td></tr>';
        return;
    }

    let totalDayShift = 0;
    let totalNightShift = 0;
    let totalProduction = 0;

    const rows = productionData.map(item => {
        const dayShift = parseInt(item.dayShift) || 0;
        const nightShift = parseInt(item.nightShift) || 0;
        const dailyTotal = dayShift + nightShift;

        totalDayShift += dayShift;
        totalNightShift += nightShift;
        totalProduction += dailyTotal;

        return `
            <tr>
                <td>${item.date}</td>
                <td>${dayShift}</td>
                <td>${nightShift}</td>
                <td><strong>${dailyTotal}</strong></td>
            </tr>
        `;
    }).join('');

    // 添加汇总行
    const summaryRow = `
        <tr class="summary-row">
            <td><strong>合计</strong></td>
            <td><strong>${totalDayShift}</strong></td>
            <td><strong>${totalNightShift}</strong></td>
            <td><strong>${totalProduction}</strong></td>
        </tr>
    `;

    tableBody.innerHTML = rows + summaryRow;
}

// 加载设备最后一条报警信息
async function loadLastAlarmMessage(deviceId) {
    try {
        const response = await fetch(`${API_BASE_URL}/api/devices/${deviceId}/last-alarm`);
        if (!response.ok) throw new Error('获取报警信息失败');

        const alarmData = await response.json();
        const alarmMessageElement = document.getElementById('lastAlarmMessage');

        if (alarmMessageElement) {
            if (alarmData && alarmData.message) {
                // 格式化报警信息显示
                const alarmTime = alarmData.startTime ? new Date(alarmData.startTime).toLocaleString('zh-CN') : '';
                const alarmMessage = alarmData.message || '未知报警';

                alarmMessageElement.innerHTML = `
                    <span class="alarm-content">${alarmMessage}</span>
                    ${alarmTime ? `<br><small class="alarm-time">时间: ${alarmTime}</small>` : ''}
                `;
                alarmMessageElement.className = 'alarm-message active';
            } else {
                alarmMessageElement.textContent = '暂无报警信息';
                alarmMessageElement.className = 'alarm-message inactive';
            }
        }
    } catch (error) {
        console.error('加载报警信息失败:', error);
        const alarmMessageElement = document.getElementById('lastAlarmMessage');
        if (alarmMessageElement) {
            alarmMessageElement.textContent = '获取报警信息失败';
            alarmMessageElement.className = 'alarm-message error';
        }
    }
}

// 添加模态框样式
function addModalStyles() {
    if (document.querySelector('#modal-styles')) return;

    const modalStyle = document.createElement('style');
    modalStyle.id = 'modal-styles';
    modalStyle.textContent = `
        .device-modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(1px);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: linear-gradient(145deg, #1a2332, #2d3748);
            border: 2px solid #4fd1c7;
            border-radius: 15px;
            padding: 20px;
            max-width: 95vw;
            width: 95vw;
            max-height: 95vh;
            height: 95vh;
            overflow-y: auto;
            overflow-x: hidden;
            position: relative;
            color: white;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            /* 隐藏滚动条但保留滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .modal-content::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .close-btn-simple {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1001;
            line-height: 1;
            user-select: none;
        }

        .close-btn-simple:hover {
            color: #ffffff;
            text-shadow: 0 0 10px #ffffff;
            transform: scale(1.1);
        }

        .close-btn-simple:active {
            transform: scale(0.95);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-header h2 {
            margin: 0;
        }

        .refresh-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #4fd1c7;
        }

        .refresh-dot {
            width: 8px;
            height: 8px;
            background: #4fd1c7;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }

        .refresh-text {
            font-size: 0.85rem;
        }

        .device-details {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            grid-template-rows: auto auto auto auto;
            gap: 15px;
            margin-bottom: 15px;
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            /* 隐藏滚动条但保留滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .device-details::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        /* 坐标和程序部分占满整行 */
        .coordinate-program-section {
            grid-column: 1 / -1;
        }

        /* 生产历史部分占满整行 */
        .production-history-section {
            grid-column: 1 / -1;
        }

        /* 刀具使用统计部分占满整行 */
        .tool-use-section {
            grid-column: 1 / -1;
        }

        .detail-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 6px;
            border: 1px solid rgba(79, 209, 199, 0.3);
        }

        .detail-section h3 {
            color: #4fd1c7;
            margin: 0 0 8px 0;
            font-size: 1rem;
            border-bottom: 1px solid rgba(79, 209, 199, 0.3);
            padding-bottom: 4px;
        }

        .detail-section p {
            margin-bottom: 4px;
            font-size: 0.85rem;
            line-height: 1.3;
        }

        .status-running { color: #48bb78; }
        .status-idle { color: #ed8936; }
        .status-alarm { color: #f56565; }
        .status-offline { color: #a0aec0; }
        .status-unknown { color: #4fd1c7; }



        .production-table {
            overflow: auto;
            max-height: 180px;
            /* 隐藏滚动条但保留滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .production-table::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        #productionTable {
            width: 100%;
            border-collapse: collapse;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            overflow: hidden;
            table-layout: fixed;
        }

        #productionTable th,
        #productionTable td {
            padding: 6px 8px;
            text-align: center;
            border-bottom: 1px solid rgba(79, 209, 199, 0.2);
            word-wrap: break-word;
        }

        #productionTable th {
            background: rgba(79, 209, 199, 0.2);
            color: #4fd1c7;
            font-weight: bold;
            font-size: 0.8rem;
        }

        #productionTable td {
            color: #e2e8f0;
            font-size: 0.75rem;
        }

        #productionTable tr:hover {
            background: rgba(79, 209, 199, 0.1);
        }

        /* 设置表格列宽 */
        #productionTable th:nth-child(1),
        #productionTable td:nth-child(1) {
            width: 25%;
        }

        #productionTable th:nth-child(2),
        #productionTable td:nth-child(2) {
            width: 25%;
        }

        #productionTable th:nth-child(3),
        #productionTable td:nth-child(3) {
            width: 25%;
        }

        #productionTable th:nth-child(4),
        #productionTable td:nth-child(4) {
            width: 25%;
            font-weight: bold;
        }

        .summary-row {
            background: rgba(79, 209, 199, 0.15) !important;
            border-top: 2px solid #4fd1c7 !important;
        }

        .summary-row td {
            color: #4fd1c7 !important;
            font-weight: bold !important;
        }

        .tool-use-table {
            overflow: auto;
            max-height: 180px;
            /* 隐藏滚动条但保留滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .tool-use-table::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .tool-use-table table {
            width: 100%;
            border-collapse: collapse;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            overflow: hidden;
            table-layout: fixed;
        }

        .tool-use-table th,
        .tool-use-table td {
            padding: 6px 8px;
            text-align: center;
            border-bottom: 1px solid rgba(79, 209, 199, 0.2);
            word-wrap: break-word;
        }

        .tool-use-table th {
            background: rgba(79, 209, 199, 0.2);
            color: #4fd1c7;
            font-weight: bold;
            font-size: 0.8rem;
        }

        .tool-use-table td {
            color: #e2e8f0;
            font-size: 0.75rem;
        }

        .tool-use-table tr:hover {
            background: rgba(79, 209, 199, 0.1);
        }

        /* 坐标和程序2x2网格显示样式 */
        .coordinate-program-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto;
            gap: 12px;
            align-items: start;
        }

        .coordinate-item,
        .program-item {
            display: flex;
            flex-direction: column;
            align-self: start;
        }

        .coordinate-item strong,
        .program-item strong {
            display: block;
            margin-bottom: 6px;
            font-size: 1rem;
            color: #4fd1c7;
            flex-shrink: 0;
        }

        .coordinate-item,
        .program-item {
            background: rgba(255, 255, 255, 0.03);
            padding: 10px;
            border-radius: 6px;
            border: 1px solid rgba(79, 209, 199, 0.2);
        }

        .coordinate-item strong,
        .program-item strong {
            color: #4fd1c7;
            display: block;
            margin-bottom: 6px;
            font-size: 0.9rem;
        }

        .coordinate-content {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 4px;
            border: 1px solid rgba(79, 209, 199, 0.1);
            font-family: 'Courier New', monospace;
            font-size: 0.95rem;
            line-height: 1.4;
            min-height: 60px;
            max-height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
            /* 隐藏滚动条但保留滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .coordinate-content::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .program-content {
            background: rgba(0, 0, 0, 0.3);
            padding: 12px;
            border-radius: 4px;
            border: 1px solid rgba(79, 209, 199, 0.1);
            font-family: 'Courier New', monospace;
            font-size: 0.95rem;
            line-height: 1.4;
            min-height: 120px;
            max-height: 200px;
            overflow-y: auto;
            overflow-x: hidden;
            white-space: pre-wrap;
            word-wrap: break-word;
            /* 隐藏滚动条但保留滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .program-content::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .coordinate-line,
        .program-line {
            color: #e2e8f0;
            margin-bottom: 3px;
            white-space: nowrap;
            font-family: 'Courier New', monospace;
        }

        .coordinate-line:last-child,
        .program-line:last-child {
            margin-bottom: 0;
        }

        .line-number {
            color: #4fd1c7;
            font-weight: bold;
            margin-right: 10px;
            display: inline-block;
            min-width: 45px;
            text-align: right;
            font-size: 0.9rem;
        }

        .no-data {
            color: #a0aec0;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }

        /* 2K分辨率适配 (2560x1440) */
        @media screen and (min-width: 2560px) {
            .modal-content {
                max-width: 95vw;
                width: 95vw;
                height: 95vh;
                padding: 25px;
            }

            .device-details {
                gap: 30px;
            }

            .detail-section {
                padding: 20px;
            }

            .detail-section h3 {
                font-size: 1.4rem;
            }

            .detail-section p {
                font-size: 1.1rem;
            }

            #productionTable th,
            #productionTable td {
                padding: 15px 20px;
                font-size: 1rem;
            }

            #productionTable th {
                font-size: 1.1rem;
            }
        }

        /* 1920*1080分辨率优化 */
        @media screen and (width: 1920px) and (height: 1080px) {
            .modal-content {
                max-width: 95vw;
                width: 95vw;
                height: 95vh;
                padding: 20px;
            }

            .device-details {
                gap: 25px;
            }

            .detail-section {
                padding: 18px;
            }

            .detail-section h3 {
                font-size: 1.3rem;
            }

            .detail-section p {
                font-size: 1rem;
            }

            #productionTable th,
            #productionTable td {
                padding: 14px 18px;
                font-size: 0.95rem;
            }

            #productionTable th {
                font-size: 1rem;
            }
        }

        /* 中等分辨率适配 */
        @media screen and (min-width: 1600px) and (max-width: 1919px) {
            .modal-content {
                max-width: 95vw;
                width: 95vw;
                height: 95vh;
                padding: 18px;
            }

            .device-details {
                gap: 22px;
            }

            .detail-section {
                padding: 16px;
            }

            #productionTable th,
            #productionTable td {
                padding: 13px 16px;
                font-size: 0.92rem;
            }
        }

        /* 小屏幕适配 */
        @media screen and (max-width: 1599px) {
            .device-details {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto auto auto auto;
                gap: 10px;
            }

            .coordinate-program-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto;
                gap: 8px;
            }

            .coordinate-program-section {
                max-height: 200px;
            }

            .production-history-section {
                max-height: 150px;
            }

            .tool-use-section {
                max-height: 150px;
            }

            .modal-content {
                width: 98vw;
                height: 98vh;
                padding: 15px;
                max-width: none;
            }
        }

            #productionTable th,
            #productionTable td {
                padding: 10px 12px;
                font-size: 0.85rem;
            }
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .device-details {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(7, auto);
                gap: 8px;
            }

            .modal-content {
                width: 98vw;
                height: 98vh;
                padding: 10px;
                margin: 1vh 1vw;
            }

            #productionTable th,
            #productionTable td {
                padding: 8px 10px;
                font-size: 0.8rem;
            }

            .detail-section {
                padding: 12px;
            }

            .detail-section p {
                font-size: 0.9rem;
            }
        }
    `;
    document.head.appendChild(modalStyle);
}

// 实时数据更新管理器
class DataUpdateManager {
    constructor() {
        this.updateIntervals = new Map();
        this.lastUpdateTimes = new Map();
        this.isUpdating = false;
        this.isPaused = false;
        this.dataCache = new Map();
        this.updateStatus = {
            ultrafast: { lastUpdate: null, errors: 0 },
            fast: { lastUpdate: null, errors: 0 },
            medium: { lastUpdate: null, errors: 0 },
            slow: { lastUpdate: null, errors: 0 }
        };
        this.maxRetries = 3;
        this.retryDelay = 2000;
    }

    // 启动数据更新 - 1分钟间隔
    startDataUpdate() {
        console.log('🚀 启动数据更新系统 (1分钟间隔)...');

        // 所有数据更新统一改为1分钟间隔
        this.scheduleUpdate('stats', () => {
            this.updateUltraFastData();
        }, 60000);

        this.scheduleUpdate('devices', () => {
            this.updateFastData();
        }, 60000);

        this.scheduleUpdate('charts', () => {
            this.updateMediumData();
        }, 60000);

        this.scheduleUpdate('calendar', () => {
            this.updateSlowData();
        }, 60000);

        // 立即执行一次完整更新
        this.updateAllData();

        // 显示更新状态
        this.showUpdateStatus('success', '数据更新已启动', '数据将每1分钟自动刷新');
    }

    // 超快速数据更新 - 包含设备统计和生产统计，确保同步
    async updateUltraFastData() {
        try {
            this.updateStatus.ultrafast = { lastUpdate: new Date(), errors: 0 };
            await Promise.all([
                this.updateDeviceStatsSmooth(),
                this.updateProductionStatsSmooth()
            ]);
            console.log('📊 设备统计和生产统计更新完成');
        } catch (error) {
            this.updateStatus.ultrafast.errors++;
            console.error('超快速数据更新失败:', error);
            this.handleUpdateError('ultrafast', error);
        }
    }

    // 调度更新任务
    scheduleUpdate(type, updateFunction, interval) {
        if (this.updateIntervals.has(type)) {
            clearInterval(this.updateIntervals.get(type));
        }

        const intervalId = setInterval(() => {
            if (this.shouldUpdate()) {
                updateFunction();
            }
        }, interval);

        this.updateIntervals.set(type, intervalId);
        console.log(`⏰ ${type} 更新任务已调度，间隔 ${interval}ms`);
    }

    // 更新快速变化的数据 - 移除生产统计（已移至超快速更新）
    async updateFastData() {
        try {
            this.updateStatus.fast = { lastUpdate: new Date(), errors: 0 };
            await Promise.all([
                this.updateLatestInfoSmooth(),
                this.updateSafetyDaysSmooth(),
                this.updateDevicesSmooth()
            ]);
            console.log('🔄 设备列表和最新信息更新完成');
        } catch (error) {
            this.updateStatus.fast.errors++;
            console.error('快速数据更新失败:', error);
            this.handleUpdateError('fast', error);
        }
    }

    // 处理更新错误
    handleUpdateError(type, error) {
        const status = this.updateStatus[type];
        if (status && status.errors >= this.maxRetries) {
            console.error(`${type} 更新连续失败 ${status.errors} 次，暂停更新`);
            this.showUpdateStatus('error', `${type} 数据更新失败`, `连续失败 ${status.errors} 次`);

            // 暂停该类型的更新一段时间
            setTimeout(() => {
                status.errors = 0;
                console.log(`${type} 更新重新启动`);
            }, this.retryDelay * status.errors);
        }
    }

    // 显示更新状态
    showUpdateStatus(status, message, details = '') {
        updateDataStatus(status, message, details);
    }

    // 更新当前时间（实时时钟）
    async updateCurrentTimeSmooth() {
        // 这个不需要API调用，直接更新时间显示
        const now = new Date();
        const timeElement = document.getElementById('current-time-display');
        if (timeElement) {
            timeElement.textContent = now.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }

    // 更新中速变化的数据 - 移除重复的更新项
    async updateMediumData() {
        try {
            this.isUpdating = true;
            await Promise.all([
                this.updateEfficiencyDataSmooth(),
                this.updateTrendDataSmooth()
            ]);
            console.log('📈 效率和趋势数据更新完成');
        } catch (error) {
            console.error('中速数据更新失败:', error);
        } finally {
            this.isUpdating = false;
        }
    }

    // 更新慢速变化的数据 - 移除设备列表（已移至快速更新）
    async updateSlowData() {
        try {
            this.isUpdating = true;
            await Promise.all([
                this.updateCalendarDataSmooth()
            ]);
            console.log('📅 日历数据更新完成');
        } catch (error) {
            console.error('慢速数据更新失败:', error);
        } finally {
            this.isUpdating = false;
        }
    }

    // 完整数据更新（初始化时使用）
    async updateAllData() {
        try {
            this.isUpdating = true;
            await loadAllData();
        } catch (error) {
            console.error('完整数据更新失败:', error);
        } finally {
            this.isUpdating = false;
        }
    }

    // 平滑更新设备统计
    async updateDeviceStatsSmooth() {
        try {
            const url = currentLineId === 'all' ?
                `${API_BASE_URL}/api/device-stats` :
                `${API_BASE_URL}/api/device-stats/by-line/${currentLineId}`;

            const response = await fetch(url);
            if (!response.ok) return;

            const newStats = await response.json();

            // 检查数据是否有变化
            const cacheKey = 'device-stats';
            const cachedStats = this.dataCache.get(cacheKey);

            if (!this.isDataChanged(cachedStats, newStats)) {
                return; // 数据未变化，跳过更新
            }

            this.dataCache.set(cacheKey, newStats);
            this.animateStatsUpdate(stats, newStats);
            stats = newStats;
        } catch (error) {
            console.error('设备统计平滑更新失败:', error);
        }
    }

    // 平滑更新最新信息
    async updateLatestInfoSmooth() {
        try {
            const response = await fetch(`${API_BASE_URL}/api/latest-info`);
            if (!response.ok) return;

            const newInfo = await response.json();

            const cacheKey = 'latest-info';
            const cachedInfo = this.dataCache.get(cacheKey);

            if (!this.isDataChanged(cachedInfo, newInfo)) {
                return;
            }

            this.dataCache.set(cacheKey, newInfo);
            this.animateLatestInfoUpdate(newInfo);
        } catch (error) {
            console.error('最新信息平滑更新失败:', error);
        }
    }

    // 平滑更新生产统计
    async updateProductionStatsSmooth() {
        try {
            const response = await fetch(`${API_BASE_URL}/api/production-stats`);
            if (!response.ok) return;

            const newData = await response.json();

            const cacheKey = 'production-stats';
            const cachedData = this.dataCache.get(cacheKey);

            if (!this.isDataChanged(cachedData, newData)) {
                return;
            }

            this.dataCache.set(cacheKey, newData);
            this.animateProductionStatsUpdate(newData);
        } catch (error) {
            console.error('生产统计平滑更新失败:', error);
        }
    }

    // 平滑更新效率数据
    async updateEfficiencyDataSmooth() {
        try {
            const response = await fetch(`${API_BASE_URL}/api/efficiency`);
            if (!response.ok) return;

            const newData = await response.json();

            const cacheKey = 'efficiency-data';
            const cachedData = this.dataCache.get(cacheKey);

            if (!this.isDataChanged(cachedData, newData)) {
                return;
            }

            this.dataCache.set(cacheKey, newData);
            this.animateEfficiencyUpdate(newData);
        } catch (error) {
            console.error('效率数据平滑更新失败:', error);
        }
    }

    // 平滑更新趋势数据
    async updateTrendDataSmooth() {
        try {
            const response = await fetch(`${API_BASE_URL}/api/trend-data`);
            if (!response.ok) return;

            const newData = await response.json();

            const cacheKey = 'trend-data';
            const cachedData = this.dataCache.get(cacheKey);

            if (!this.isDataChanged(cachedData, newData)) {
                return;
            }

            this.dataCache.set(cacheKey, newData);
            window.trendData = newData;
            this.updateChartsSmooth();
        } catch (error) {
            console.error('趋势数据平滑更新失败:', error);
        }
    }

    // 平滑更新设备列表
    async updateDevicesSmooth() {
        try {
            const url = currentLineId === 'all' ?
                `${API_BASE_URL}/api/devices` :
                `${API_BASE_URL}/api/devices/by-line/${currentLineId}`;

            const response = await fetch(url);
            if (!response.ok) return;

            const newDevices = await response.json();

            const cacheKey = 'devices';
            const cachedDevices = this.dataCache.get(cacheKey);

            if (!this.isDataChanged(cachedDevices, newDevices)) {
                return;
            }

            this.dataCache.set(cacheKey, newDevices);
            this.animateDevicesUpdate(newDevices);
        } catch (error) {
            console.error('设备列表平滑更新失败:', error);
        }
    }

    // 平滑更新日历数据
    async updateCalendarDataSmooth() {
        try {
            await loadCalendarAlarmData();
        } catch (error) {
            console.error('日历数据平滑更新失败:', error);
        }
    }

    // 平滑更新安全生产天数
    async updateSafetyDaysSmooth() {
        try {
            const response = await fetch(`${API_BASE_URL}/api/safety-days`);
            if (!response.ok) return;

            const newData = await response.json();

            const cacheKey = 'safety-days';
            const cachedData = this.dataCache.get(cacheKey);

            if (!this.isDataChanged(cachedData, newData)) {
                return;
            }

            this.dataCache.set(cacheKey, newData);
            this.animateSafetyDaysUpdate(newData);
        } catch (error) {
            console.error('安全生产天数平滑更新失败:', error);
        }
    }

    // 动画更新安全生产天数
    animateSafetyDaysUpdate(newData) {
        const safetyDaysEl = document.getElementById('safety-days');
        if (!safetyDaysEl) return;

        const currentValue = parseInt(safetyDaysEl.textContent) || 0;
        const newValue = newData.safetyDays || 0;

        if (currentValue !== newValue) {
            this.animateNumberChange(safetyDaysEl, currentValue, newValue, 2000);
        }

        // 如果有备注信息，可以在控制台显示
        if (newData.note) {
            console.log('安全生产天数说明:', newData.note);
        }
    }

    // 检查数据是否有变化
    isDataChanged(oldData, newData) {
        if (!oldData) return true;
        return JSON.stringify(oldData) !== JSON.stringify(newData);
    }

    // 动画更新统计数据
    animateStatsUpdate(oldStats, newStats) {
        const elements = {
            running: document.getElementById('running-count'),
            idle: document.getElementById('idle-count'),
            alarm: document.getElementById('alarm-count'),
            offline: document.getElementById('offline-count')
        };

        Object.keys(elements).forEach(key => {
            const element = elements[key];
            if (element && oldStats[key] !== newStats[key]) {
                this.animateNumberChange(element, oldStats[key] || 0, newStats[key] || 0);
            }
        });

        // 更新设备状态环形图
        this.createEfficiencyChart(newStats);
    }

    // 动画更新最新信息
    animateLatestInfoUpdate(newInfo) {
        const container = document.getElementById('latest-info');
        if (!container) return;

        // 添加淡出效果
        container.style.opacity = '0.7';

        setTimeout(() => {
            renderLatestInfo(newInfo);
            // 添加淡入效果
            container.style.opacity = '1';
        }, 200);
    }

    // 动画更新生产统计 - 保持自动滚动效果
    animateProductionStatsUpdate(newData) {
        const container = document.getElementById('production-stats');
        if (!container) return;

        // 检查是否需要更新数据
        const existingItems = container.querySelectorAll('.device-shift-item');
        let hasChanges = false;

        if (!newData.devices || newData.devices.length !== existingItems.length) {
            hasChanges = true;
        } else {
            // 检查数据是否有变化
            newData.devices.forEach((device, index) => {
                const existingItem = existingItems[index];
                if (existingItem) {
                    const currentDayShift = existingItem.querySelector('.day-shift')?.textContent;
                    const currentNightShift = existingItem.querySelector('.night-shift')?.textContent;
                    const currentTotal = existingItem.querySelector('.total-shift')?.textContent;

                    if (currentDayShift !== device.dayShift.toString() ||
                        currentNightShift !== device.nightShift.toString() ||
                        currentTotal !== device.total.toString()) {
                        hasChanges = true;
                    }
                }
            });
        }

        // 如果没有变化，直接返回
        if (!hasChanges) {
            return;
        }

        // 优先使用就地更新，避免破坏自动滚动
        if (existingItems.length === newData.devices.length && existingItems.length > 0) {
            // 就地更新数字，不重新渲染DOM结构
            newData.devices.forEach((device, index) => {
                const existingItem = existingItems[index];
                if (existingItem) {
                    const dayShiftEl = existingItem.querySelector('.day-shift');
                    const nightShiftEl = existingItem.querySelector('.night-shift');
                    const totalEl = existingItem.querySelector('.total-shift');

                    if (dayShiftEl && dayShiftEl.textContent !== device.dayShift.toString()) {
                        this.animateNumberChange(dayShiftEl, parseInt(dayShiftEl.textContent) || 0, device.dayShift, 500);
                    }
                    if (nightShiftEl && nightShiftEl.textContent !== device.nightShift.toString()) {
                        this.animateNumberChange(nightShiftEl, parseInt(nightShiftEl.textContent) || 0, device.nightShift, 500);
                    }
                    if (totalEl && totalEl.textContent !== device.total.toString()) {
                        this.animateNumberChange(totalEl, parseInt(totalEl.textContent) || 0, device.total, 500);
                    }
                }
            });

            // 移除汇总数据更新

            // 更新完成率
            if (newData.summary) {
                const completionRate = Math.min(100, Math.round((newData.summary.totalProduction / 1000) * 100));
                updateCompletionRate(completionRate);
            }

            return;
        }

        // 只有在结构发生变化时才重新渲染（保持自动滚动）
        this.renderProductionStatsWithAutoScroll(container, newData);
    }

    // 渲染生产统计并保持自动滚动
    renderProductionStatsWithAutoScroll(container, newData) {
        if (!newData.devices || newData.devices.length === 0) {
            return;
        }

        let html = `
            <div class="shift-header">
                <span class="device-col">设备名</span>
                <span class="shift-col">白班</span>
                <span class="shift-col">夜班</span>
                <span class="total-col">总数</span>
            </div>
        `;

        newData.devices.forEach(device => {
            html += `
                <div class="device-shift-item">
                    <span class="device-name-shift">${device.devname}</span>
                    <span class="day-shift">${device.dayShift}</span>
                    <span class="night-shift">${device.nightShift}</span>
                    <span class="total-shift">${device.total}</span>
                </div>
            `;
        });

        // 移除汇总信息

        // 确定是否需要自动滚动
        const needAutoScroll = newData.devices.length > 5;
        const scrollClass = 'production-stats-content';
        const scrollAttr = needAutoScroll ? '' : 'data-scroll="false"';
        const finalHtml = `<div class="${scrollClass}" ${scrollAttr}>${html}</div>`;

        console.log(`📊 生产统计渲染: ${newData.devices.length}个设备, 自动滚动: ${needAutoScroll ? '启用' : '禁用'}`);

        // 直接更新内容，不使用淡入淡出效果，保持自动滚动
        container.innerHTML = finalHtml;

        // 动态调整滚动动画
        this.adjustAutoScrollAnimation(container, needAutoScroll);

        // 更新完成率
        if (newData.summary) {
            const completionRate = Math.min(100, Math.round((newData.summary.totalProduction / 1000) * 100));
            updateCompletionRate(completionRate);
        }
    }

    // 动态调整自动滚动动画
    adjustAutoScrollAnimation(container, needAutoScroll) {
        const contentContainer = container.querySelector('.production-stats-content');
        if (!contentContainer) return;

        // 计算内容高度和容器高度
        const containerHeight = container.offsetHeight;
        const contentHeight = contentContainer.scrollHeight;

        if (contentHeight <= containerHeight) {
            // 内容不够高，不需要滚动
            contentContainer.style.animation = 'none';
            console.log('🔄 内容高度不足，禁用自动滚动');
            return;
        }

        // 启用自动滚动动画
        const animationDuration = 30; // 固定30秒循环
        contentContainer.style.animation = `autoScroll ${animationDuration}s linear infinite`;

        console.log(`🔄 自动滚动启用: 容器高度=${containerHeight}px, 内容高度=${contentHeight}px, 动画时长=${animationDuration}s`);
    }

    // 动画更新效率数据
    animateEfficiencyUpdate(newData) {
        // 更新设备状态环形图
        this.updateEfficiencyChart();
    }

    // 动画更新设备列表
    animateDevicesUpdate(newDevices) {
        const container = document.getElementById('device-grid');
        if (!container) return;

        container.style.opacity = '0.7';

        setTimeout(() => {
            renderDeviceCards(newDevices);
            container.style.opacity = '1';
        }, 200);
    }

    // 平滑更新图表
    updateChartsSmooth() {
        setTimeout(() => {
            initCharts();
        }, 100);
    }

    // 数字变化动画
    animateNumberChange(element, fromValue, toValue, duration = 1000) {
        const startTime = performance.now();
        const difference = toValue - fromValue;

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeProgress = this.easeOutCubic(progress);
            const currentValue = Math.round(fromValue + (difference * easeProgress));

            element.textContent = currentValue;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    // 缓动函数
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }

    // 更新设备状态环形图
    updateEfficiencyChart() {
        // 使用当前的设备统计数据
        if (typeof stats !== 'undefined' && stats) {
            this.createEfficiencyChart(stats);
        }
    }

    // 创建设备状态环形图
    createEfficiencyChart(deviceStats) {
        const canvas = document.getElementById('efficiencyChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // 销毁现有图表
        if (window.efficiencyChart && typeof window.efficiencyChart.destroy === 'function') {
            window.efficiencyChart.destroy();
        }

        // 准备数据
        const data = {
            labels: ['运行', '空闲', '报警', '离线'],
            datasets: [{
                data: [
                    deviceStats.running || 0,
                    deviceStats.idle || 0,
                    deviceStats.alarm || 0,
                    deviceStats.offline || 0
                ],
                backgroundColor: [
                    '#48bb78', // 运行 - 绿色
                    '#ed8936', // 空闲 - 橙色
                    '#f56565', // 报警 - 红色
                    '#a0aec0'  // 离线 - 灰色
                ],
                borderColor: [
                    '#48bb78',
                    '#ed8936',
                    '#f56565',
                    '#a0aec0'
                ],
                borderWidth: 2
            }]
        };

        // 创建环形图
        window.efficiencyChart = new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        bottom: Math.max(5, Math.min(15, window.innerWidth / 200)) // 为图例留出空间
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        align: 'center',
                        labels: {
                            color: '#ffffff',
                            font: {
                                size: Math.max(7, Math.min(11, window.innerWidth / 200)) // 进一步优化字体大小
                            },
                            padding: Math.max(2, Math.min(6, window.innerWidth / 300)), // 进一步减小间距
                            usePointStyle: true,
                            pointStyle: 'circle',
                            boxWidth: Math.max(8, Math.min(12, window.innerWidth / 200)), // 自适应图例框大小
                            boxHeight: Math.max(8, Math.min(12, window.innerWidth / 200))
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value}台 (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 1000
                },
                cutout: '60%' // 环形图的内圆大小
            }
        });

        console.log('📊 设备状态环形图已更新:', deviceStats);
    }

    // 暂停所有更新
    pauseUpdates() {
        this.isPaused = true;
        console.log('⏸️ 实时更新已暂停');
    }

    // 恢复所有更新
    resumeUpdates() {
        this.isPaused = false;
        console.log('▶️ 实时更新已恢复');
    }

    // 检查是否应该执行更新
    shouldUpdate() {
        return !this.isPaused && !this.isUpdating;
    }

    // 停止所有更新
    stopAllUpdates() {
        this.updateIntervals.forEach((intervalId) => {
            clearInterval(intervalId);
        });
        this.updateIntervals.clear();
        console.log('🛑 所有实时更新已停止');
    }
}

// 创建全局数据更新管理器实例
const dataUpdateManager = new DataUpdateManager();
window.dataUpdateManager = dataUpdateManager;

// 开始数据更新
function startDataUpdate() {
    dataUpdateManager.startDataUpdate();
}

// 显示错误信息
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-notification';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f56565;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 9999;
        box-shadow: 0 4px 20px rgba(245, 101, 101, 0.3);
    `;

    document.body.appendChild(errorDiv);

    setTimeout(() => {
        if (document.body.contains(errorDiv)) {
            document.body.removeChild(errorDiv);
        }
    }, 5000);
}

// 添加页面特效
function addPageEffects() {
    // 添加鼠标移动视差效果
    document.addEventListener('mousemove', (e) => {
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;

        if (particles) {
            particles.rotation.x = mouseY * 0.1;
            particles.rotation.y = mouseX * 0.1;
        }
    });

    // 添加滚动时的视差效果
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;

        if (particles) {
            particles.position.y = rate * 0.1;
        }
    });
}

// 页面加载完成后添加特效
window.addEventListener('load', addPageEffects);

// 初始化天气
function initWeather() {
    // 获取用户地理位置
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lon = position.coords.longitude;
                console.log('获取到位置:', lat, lon);
                getWeatherData(lat, lon);
                getCityName(lat, lon);
            },
            function(error) {
                console.log('获取位置失败:', error);
                showDefaultWeather();
            },
            {
                timeout: 10000,
                enableHighAccuracy: false
            }
        );
    } else {
        console.log('浏览器不支持地理定位');
        showDefaultWeather();
    }
}

// 获取天气数据
async function getWeatherData(lat, lon) {
    try {
        // 方式1: 使用真实天气API (需要申请API key)
        /*
        const API_KEY = 'YOUR_OPENWEATHER_API_KEY'; // 在 https://openweathermap.org/api 申请
        const response = await fetch(`https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${API_KEY}&units=metric&lang=zh_cn`);

        if (response.ok) {
            const data = await response.json();
            const weatherData = {
                temperature: Math.round(data.main.temp),
                description: data.weather[0].description,
                icon: data.weather[0].icon
            };
            updateWeatherDisplay(weatherData);
            return;
        }
        */

        // 方式2: 模拟天气数据 (当前使用)
        const mockWeatherData = {
            temperature: Math.round(15 + Math.random() * 20), // 15-35度随机温度
            description: ['晴朗', '多云', '阴天', '小雨'][Math.floor(Math.random() * 4)],
            icon: '☀️'
        };

        updateWeatherDisplay(mockWeatherData);

    } catch (error) {
        console.error('获取天气数据失败:', error);
        showDefaultWeather();
    }
}

// 获取城市名称
async function getCityName(lat, lon) {
    try {
        // 方式1: 使用真实地理编码API
        /*
        const response = await fetch(`https://api.openweathermap.org/geo/1.0/reverse?lat=${lat}&lon=${lon}&limit=1&appid=${API_KEY}`);

        if (response.ok) {
            const data = await response.json();
            const cityName = data[0]?.local_names?.zh || data[0]?.name || '当前位置';
            document.getElementById('weather-location').textContent = cityName;
            return;
        }
        */

        // 方式2: 模拟位置数据 (当前使用)
        const mockLocation = '当前位置';
        document.getElementById('weather-location').textContent = mockLocation;

    } catch (error) {
        console.error('获取城市名称失败:', error);
        document.getElementById('weather-location').textContent = '未知位置';
    }
}

// 更新天气显示
function updateWeatherDisplay(weatherData) {
    document.getElementById('weather-temp').textContent = `${weatherData.temperature}°C`;
    document.getElementById('weather-desc').textContent = weatherData.description;
}

// 显示默认天气
function showDefaultWeather() {
    document.getElementById('weather-location').textContent = '本地';
    document.getElementById('weather-temp').textContent = '23°C';
    document.getElementById('weather-desc').textContent = '晴朗';
}

// 加载安全生产天数
async function loadSafetyDays() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/safety-days`);
        if (!response.ok) throw new Error('获取安全生产天数失败');

        const safetyData = await response.json();
        const safetyDaysEl = document.getElementById('safety-days');

        if (safetyDaysEl) {
            safetyDaysEl.textContent = safetyData.safetyDays || 0;
        }

        // 如果有备注信息，在控制台显示
        if (safetyData.note) {
            console.log('安全生产天数说明:', safetyData.note);
        }

        console.log('安全生产天数加载完成:', safetyData.safetyDays);
    } catch (error) {
        console.error('加载安全生产天数失败:', error);
        const safetyDaysEl = document.getElementById('safety-days');
        if (safetyDaysEl) {
            safetyDaysEl.textContent = '0';
        }
    }
}

// 加载所有数据 - 优化版本支持实时刷新
async function loadAllData() {
    const startTime = performance.now();
    updateDataStatus('loading', '🔄 正在加载实时数据...', '从数据库获取最新数据');

    try {
        console.log('📊 开始加载所有数据...');

        // 分批加载数据以提高性能
        const criticalData = [
            { name: '设备统计', fn: loadDeviceStats },
            { name: '设备列表', fn: loadDevices },
            { name: '生产统计', fn: loadProductionStats }
        ];

        const secondaryData = [
            { name: '效率数据', fn: loadEfficiencyData },
            { name: '最新信息', fn: loadLatestInfo },
            { name: '安全天数', fn: loadSafetyDays }
        ];

        const backgroundData = [
            { name: '趋势数据', fn: loadTrendData },
            { name: '日历数据', fn: loadCalendarAlarmData }
        ];

        // 第一批：关键数据（并行加载）
        console.log('⚡ 加载关键数据...');
        const criticalResults = await Promise.allSettled(
            criticalData.map(item => item.fn().catch(error => {
                console.error(`${item.name}加载失败:`, error);
                throw new Error(`${item.name}: ${error.message}`);
            }))
        );

        // 第二批：次要数据（并行加载）
        console.log('🔄 加载次要数据...');
        const secondaryResults = await Promise.allSettled(
            secondaryData.map(item => item.fn().catch(error => {
                console.error(`${item.name}加载失败:`, error);
                throw new Error(`${item.name}: ${error.message}`);
            }))
        );

        // 第三批：后台数据（异步加载，不阻塞UI）
        console.log('📈 后台加载图表数据...');
        Promise.allSettled(
            backgroundData.map(item => item.fn().catch(error => {
                console.error(`${item.name}加载失败:`, error);
            }))
        ).then(() => {
            // 更新图表
            setTimeout(() => {
                initCharts();
                console.log('📊 图表更新完成');
            }, 100);
        });

        // 统计结果
        const allResults = [...criticalResults, ...secondaryResults];
        const failed = allResults.filter(result => result.status === 'rejected');
        const succeeded = allResults.filter(result => result.status === 'fulfilled');

        const loadTime = Math.round(performance.now() - startTime);
        console.log(`📊 数据加载完成: 成功${succeeded.length}项，失败${failed.length}项，耗时${loadTime}ms`);

        // 更新状态指示器
        if (failed.length === 0) {
            updateDataStatus('success', '✅ 实时数据加载成功', `${succeeded.length}项数据已更新 (${loadTime}ms)`);
        } else if (succeeded.length > failed.length) {
            updateDataStatus('warning', '⚠️ 部分数据加载成功', `成功${succeeded.length}项，失败${failed.length}项`);
        } else {
            updateDataStatus('error', '❌ 大部分数据加载失败', `仅成功${succeeded.length}项，失败${failed.length}项`);
        }

        // 记录失败的项目
        if (failed.length > 0) {
            const failedItems = failed.map((f, index) => {
                const itemName = index < criticalData.length ?
                    criticalData[index].name :
                    secondaryData[index - criticalData.length].name;
                return `${itemName}: ${f.reason}`;
            });
            console.warn('❌ 数据加载失败项目:', failedItems);
        }

        return { succeeded: succeeded.length, failed: failed.length, loadTime };

    } catch (error) {
        const loadTime = Math.round(performance.now() - startTime);
        console.error('💥 数据加载异常:', error);
        updateDataStatus('error', '💥 数据加载异常', `请检查网络连接和数据库状态 (${loadTime}ms)`);
        showError('数据加载异常，请检查网络连接');
        throw error;
    }
}

// 完整的日历功能实现

// 初始化日历
function initCalendar() {
    // 生成当前月的日历
    generateCalendar(currentCalendarDate);

    // 绑定月份导航事件
    document.getElementById('prev-month').addEventListener('click', () => {
        currentCalendarDate.setMonth(currentCalendarDate.getMonth() - 1);
        generateCalendar(currentCalendarDate);
        loadCalendarAlarmData();
    });

    document.getElementById('next-month').addEventListener('click', () => {
        currentCalendarDate.setMonth(currentCalendarDate.getMonth() + 1);
        generateCalendar(currentCalendarDate);
        loadCalendarAlarmData();
    });
}

// 生成月历
function generateCalendar(date) {
    const year = date.getFullYear();
    const month = date.getMonth();

    // 更新月份标题
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月',
                       '7月', '8月', '9月', '10月', '11月', '12月'];
    document.getElementById('current-month-year').textContent = `${year}年${monthNames[month]}`;

    // 获取月份的第一天和最后一天
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startDayOfWeek = firstDay.getDay();

    // 获取上个月的天数（为了填充第一周）
    const prevMonth = new Date(year, month - 1, 0);
    const daysInPrevMonth = prevMonth.getDate();

    const calendarDays = document.getElementById('calendar-days');
    calendarDays.innerHTML = '';

    // 填充上个月的日期
    for (let i = startDayOfWeek - 1; i >= 0; i--) {
        const dayNum = daysInPrevMonth - i;
        const dayElement = createDayElement(dayNum, 'other-month');
        calendarDays.appendChild(dayElement);
    }

    // 填充当前月的日期
    const today = new Date();
    for (let day = 1; day <= daysInMonth; day++) {
        const dayElement = createDayElement(day, 'current-month');

        // 检查是否是今天
        if (year === today.getFullYear() && month === today.getMonth() && day === today.getDate()) {
            dayElement.classList.add('today');
        }

        // 检查是否有异常
        const dateKey = formatDateKey(year, month, day);
        if (alarmDates.has(dateKey)) {
            dayElement.classList.add('has-alarm');
        }

        // 添加点击事件
        dayElement.addEventListener('click', () => {
            showAlarmModal(new Date(year, month, day));
        });

        calendarDays.appendChild(dayElement);
    }

    // 填充下个月的日期（为了填满最后一周）
    const totalCells = 42; // 6行 × 7列
    const usedCells = startDayOfWeek + daysInMonth;
    const remainingCells = totalCells - usedCells;

    for (let day = 1; day <= remainingCells && remainingCells < 14; day++) {
        const dayElement = createDayElement(day, 'other-month');
        calendarDays.appendChild(dayElement);
    }
}

// 创建日期元素
function createDayElement(day, monthType) {
    const dayElement = document.createElement('div');
    dayElement.className = `calendar-day ${monthType}`;
    dayElement.textContent = day;
    return dayElement;
}

// 格式化日期键
function formatDateKey(year, month, day) {
    return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
}

// 加载日历异常数据
async function loadCalendarAlarmData() {
    try {
        const year = currentCalendarDate.getFullYear();
        const month = currentCalendarDate.getMonth() + 1;

        console.log(`正在加载日历异常数据: ${year}年${month}月`);

        const response = await fetch(`${API_BASE_URL}/api/calendar-alarms/${year}/${month}`);
        if (!response.ok) throw new Error('获取日历异常数据失败');

        const alarmData = await response.json();
        console.log('日历异常数据:', alarmData);

        // 更新异常日期集合
        alarmDates.clear();
        if (alarmData.alarmDates && Array.isArray(alarmData.alarmDates)) {
            alarmData.alarmDates.forEach(date => {
                alarmDates.add(date);
                console.log('添加异常日期:', date);
            });
        }

        // 重新生成日历以更新异常标记
        generateCalendar(currentCalendarDate);

        console.log('日历异常数据加载完成:', alarmData);
        console.log('异常日期集合:', Array.from(alarmDates));
    } catch (error) {
        console.error('加载日历异常数据失败:', error);
        // 出错时清空异常数据
        alarmDates.clear();
        generateCalendar(currentCalendarDate);
    }
}

// 显示异常信息弹窗
async function showAlarmModal(date) {
    console.log('点击日期:', date);

    const modal = document.getElementById('alarm-modal');
    const modalTitle = document.getElementById('modal-title');

    if (!modal) {
        console.error('找不到弹窗元素 #alarm-modal');
        alert('弹窗组件未找到，请检查页面结构');
        return;
    }

    if (!modalTitle) {
        console.error('找不到弹窗标题元素 #modal-title');
        return;
    }

    // 格式化日期标题
    const dateStr = date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    modalTitle.textContent = `${dateStr} - 异常报告`;
    console.log('设置弹窗标题:', modalTitle.textContent);

    // 显示加载状态
    const totalAlarmsEl = document.getElementById('total-alarms');
    const affectedDevicesEl = document.getElementById('affected-devices');
    const totalDurationEl = document.getElementById('total-duration');
    const alarmListEl = document.getElementById('alarm-list');

    if (totalAlarmsEl) totalAlarmsEl.textContent = '加载中...';
    if (affectedDevicesEl) affectedDevicesEl.textContent = '加载中...';
    if (totalDurationEl) totalDurationEl.textContent = '加载中...';
    if (alarmListEl) alarmListEl.innerHTML = '<div class="loading">正在加载报警详情...</div>';

    // 显示弹窗并禁止body滚动
    modal.style.display = 'block';
    document.body.classList.add('modal-open');
    console.log('弹窗已显示，body滚动已禁止');

    try {
        // 获取该日期的详细异常信息
        const dateKey = formatDateKey(date.getFullYear(), date.getMonth(), date.getDate());
        console.log('查询日期键:', dateKey);

        const response = await fetch(`${API_BASE_URL}/api/daily-alarms/${dateKey}`);
        console.log('API响应状态:', response.status);

        if (!response.ok) throw new Error('获取异常详情失败');

        const alarmDetails = await response.json();
        console.log('异常详情数据:', alarmDetails);

        // 更新统计信息
        if (totalAlarmsEl) totalAlarmsEl.textContent = alarmDetails.totalAlarms || 0;
        if (affectedDevicesEl) affectedDevicesEl.textContent = alarmDetails.affectedDevices || 0;
        if (totalDurationEl) totalDurationEl.textContent = alarmDetails.totalDuration || '0分钟';

        // 更新报警列表
        renderAlarmList(alarmDetails.alarms || []);

        // 绘制报警趋势图
        renderAlarmChart(alarmDetails.hourlyStats || []);

        console.log('弹窗数据更新完成');

    } catch (error) {
        console.error('加载异常详情失败:', error);

        // 显示错误信息
        if (totalAlarmsEl) totalAlarmsEl.textContent = '0';
        if (affectedDevicesEl) affectedDevicesEl.textContent = '0';
        if (totalDurationEl) totalDurationEl.textContent = '0分钟';
        if (alarmListEl) alarmListEl.innerHTML = '<div class="error">异常详情加载失败: ' + error.message + '</div>';
    }
}

// 渲染报警列表
function renderAlarmList(alarms) {
    console.log('渲染报警列表，数据:', alarms);

    const alarmList = document.getElementById('alarm-list');
    if (!alarmList) {
        console.error('找不到报警列表元素 #alarm-list');
        return;
    }

    alarmList.innerHTML = '';

    if (!alarms || alarms.length === 0) {
        alarmList.innerHTML = '<div class="no-data">该日期无异常记录</div>';
        console.log('无异常记录');
        return;
    }

    console.log(`开始渲染${alarms.length}条异常记录`);

    // 创建表格容器
    const tableContainer = document.createElement('div');
    tableContainer.className = 'alarm-table-container';
    
    const table = document.createElement('table');
    table.className = 'alarm-table';
    
    // 创建表头
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th class="device-col">设备</th>
            <th class="content-col">报警内容</th>
            <th class="time-col">时间</th>
            <th class="status-col">状态</th>
            <th class="duration-col">持续时间</th>
            <th class="severity-col">级别</th>
        </tr>
    `;
    table.appendChild(thead);
    
    // 创建表体
    const tbody = document.createElement('tbody');
    
    alarms.forEach((alarm, index) => {
        console.log(`渲染第${index + 1}条异常:`, alarm);

        const row = document.createElement('tr');
        row.className = 'alarm-row';

        // 根据报警内容确定严重级别
        const severity = getAlarmSeverity(alarm.alarmcontent);

        // 构建时间显示信息
        let timeDisplay = alarm.alarmtime || '未知时间';
        let statusDisplay = '';
        
        if (alarm.status) {
            const statusIcons = {
                '开始': '🔴',
                '持续中': '🟡',
                '结束': '🟢'
            };
            
            statusDisplay = `<span class="alarm-status-table ${alarm.status}">${statusIcons[alarm.status] || '⚪'} ${alarm.status}</span>`;
            
            if (alarm.status === '持续中' && alarm.endtime) {
                timeDisplay += ` → ${alarm.endtime}`;
            } else if (alarm.status === '结束' && alarm.endtime) {
                timeDisplay = `${timeDisplay} → ${alarm.endtime}`;
            }
        }

        row.innerHTML = `
            <td class="device-col">
                <div class="device-name-table">${alarm.devname || alarm.devno || '未知设备'}</div>
                ${alarm.createtime ? `<div class="create-time-table">开始: ${alarm.createtime}</div>` : ''}
            </td>
            <td class="content-col">
                <div class="alarm-content-table">${alarm.alarmcontent || '无报警内容'}</div>
            </td>
            <td class="time-col">
                <div class="time-display-table">${timeDisplay}</div>
            </td>
            <td class="status-col">
                ${statusDisplay}
            </td>
            <td class="duration-col">
                <div class="duration-display-table">${alarm.duration || '未知'}</div>
            </td>
            <td class="severity-col">
                <span class="alarm-severity-table ${severity}">${getSeverityText(severity)}</span>
            </td>
        `;

        tbody.appendChild(row);
    });
    
    table.appendChild(tbody);
    tableContainer.appendChild(table);
    alarmList.appendChild(tableContainer);

    console.log('报警列表渲染完成');
}

// 获取报警严重级别
function getAlarmSeverity(content) {
    const highKeywords = ['紧急', '严重', '故障', '停机', '错误'];
    const mediumKeywords = ['警告', '异常', '超限', '偏差'];

    const lowerContent = content.toLowerCase();

    if (highKeywords.some(keyword => lowerContent.includes(keyword))) {
        return 'high';
    } else if (mediumKeywords.some(keyword => lowerContent.includes(keyword))) {
        return 'medium';
    }
    return 'low';
}

// 获取严重级别文本
function getSeverityText(severity) {
    switch (severity) {
        case 'high': return '高危';
        case 'medium': return '中等';
        case 'low': return '轻微';
        default: return '未知';
    }
}

// 渲染报警趋势图
function renderAlarmChart(hourlyStats) {
    // 检查Chart.js是否已加载
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js 尚未加载，无法渲染报警趋势图');
        return;
    }

    const chartElement = document.getElementById('alarmTrendChart');
    if (!chartElement) {
        console.warn('未找到报警趋势图元素');
        return;
    }

    const ctx = chartElement.getContext('2d');

    // 销毁旧图表
    if (alarmChart && typeof alarmChart.destroy === 'function') {
        alarmChart.destroy();
    }

    // 准备数据
    const labels = Array.from({length: 24}, (_, i) => `${i}:00`);
    const data = new Array(24).fill(0);

    // 填充实际数据
    if (hourlyStats) {
        hourlyStats.forEach(stat => {
            if (stat.hour >= 0 && stat.hour < 24) {
                data[stat.hour] = stat.count;
            }
        });
    }

    // 创建新图表
    alarmChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '报警数量',
                data: data,
                borderColor: '#f56565',
                backgroundColor: 'rgba(245, 101, 101, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff'
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: '#a0aec0',
                        stepSize: 1
                    },
                    grid: {
                        color: 'rgba(79, 209, 199, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: '#a0aec0'
                    },
                    grid: {
                        color: 'rgba(79, 209, 199, 0.1)'
                    }
                }
            }
        }
    });
}

// 初始化模态框事件
function initModalEvents() {
    const modal = document.getElementById('alarm-modal');
    const closeBtn = document.getElementById('close-modal');

    // 关闭弹窗的通用函数
    function closeModal() {
        if (modal) {
            modal.style.display = 'none';
            document.body.classList.remove('modal-open');
            console.log('弹窗已关闭，body滚动已恢复');
        }
    }

    // 关闭按钮事件
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }

    // 点击外部关闭
    window.addEventListener('click', (event) => {
        if (event.target === modal) {
            closeModal();
        }
    });

    // ESC键关闭
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape' && modal && modal.style.display === 'block') {
            closeModal();
        }
    });

    console.log('弹窗事件监听器已初始化');
}