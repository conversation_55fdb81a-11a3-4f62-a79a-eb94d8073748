/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* 登录页面样式 */
.login-body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-box {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.login-box h2 {
    margin-bottom: 30px;
    color: #333;
    font-size: 24px;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.login-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: transform 0.2s;
}

.login-btn:hover {
    transform: translateY(-2px);
}

.message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 5px;
    font-size: 14px;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 后台管理界面样式 */
.admin-container {
    display: flex;
    height: 100vh;
}

/* 侧边栏 */
.sidebar {
    width: 250px;
    background-color: #2c3e50;
    color: white;
    flex-shrink: 0;
}

.sidebar-header {
    padding: 20px;
    background-color: #34495e;
    text-align: center;
}

.sidebar-header h3 {
    font-size: 18px;
    font-weight: 500;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
}

.sidebar-menu li {
    border-bottom: 1px solid #34495e;
}

.sidebar-menu a {
    display: block;
    padding: 15px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background-color: #34495e;
    color: white;
    border-left: 3px solid #3498db;
}

/* 子菜单样式 */
.menu-item-with-submenu {
    position: relative;
}

.submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: #1a252f;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.submenu.active {
    max-height: 200px;
}

.submenu li {
    border-bottom: none;
}

.submenu a {
    padding: 12px 40px;
    font-size: 14px;
    color: #95a5a6;
    border-left: none;
}

.submenu a:hover,
.submenu a.active {
    background-color: #2c3e50;
    color: #3498db;
    border-left: 3px solid #3498db;
}

.menu-item-with-submenu > a::after {
    content: "▼";
    float: right;
    font-size: 12px;
    transition: transform 0.3s ease;
}

.menu-item-with-submenu.expanded > a::after {
    transform: rotate(180deg);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 顶部导航 */
.top-header {
    background-color: white;
    padding: 0 30px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.header-left h1 {
    font-size: 20px;
    color: #2c3e50;
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    color: #7f8c8d;
    font-size: 14px;
}

.logout-btn {
    padding: 8px 16px;
    background-color: #e74c3c;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    transition: background-color 0.3s;
}

.logout-btn:hover {
    background-color: #c0392b;
}

/* 内容区域 */
.content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background-color: #f8f9fa;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* 仪表盘欢迎页面样式 */
.dashboard-welcome {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 40px 20px;
}

.welcome-card {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 100%;
    text-align: center;
}

.welcome-card h2 {
    color: #2c3e50;
    font-size: 28px;
    margin-bottom: 15px;
    font-weight: 600;
}

.welcome-card > p {
    color: #7f8c8d;
    font-size: 16px;
    margin-bottom: 40px;
}

.welcome-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-top: 30px;
    text-align: left;
}

.info-item h4 {
    color: #3498db;
    font-size: 18px;
    margin-bottom: 15px;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 8px;
}

.info-item ul {
    list-style: none;
    padding: 0;
}

.info-item li {
    color: #5a6c7d;
    padding: 8px 0;
    border-bottom: 1px solid #ecf0f1;
    position: relative;
    padding-left: 20px;
}

.info-item li:before {
    content: "•";
    color: #3498db;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.info-item li:last-child {
    border-bottom: none;
}

/* 卡片样式 */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.card {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 18px;
}

/* 表格样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 500;
}

.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th,
table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

table th {
    background-color: #f8f9fa;
    font-weight: 500;
    color: #2c3e50;
}

table tr:hover {
    background-color: #f8f9fa;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

/* 表单样式 */
.export-form,
.profile-form {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    align-items: end;
}

.form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 导出结果 */
.export-result {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px 30px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

.modal form {
    padding: 30px;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* 权限控制 */
.admin-only {
    display: none;
}

/* 侧边栏菜单项的权限控制 */
body.is-admin .sidebar-menu .admin-only {
    display: list-item;
}

/* 内容区域的权限控制 - 只有激活的页面才显示 */
body.is-admin .content-section.admin-only.active {
    display: block;
}

/* 确保非激活的管理员页面仍然隐藏 */
.content-section.admin-only:not(.active) {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
    }

    .form-row {
        flex-direction: column;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .welcome-info {
        grid-template-columns: 1fr;
    }

    .welcome-card {
        padding: 30px 20px;
    }

    .welcome-card h2 {
        font-size: 24px;
    }
}

/* 子菜单样式增强 */
.has-submenu {
    position: relative;
}

.has-submenu > a {
    position: relative;
    padding-right: 50px !important;
}

.submenu-arrow {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    transition: transform 0.3s ease;
}

.has-submenu.expanded .submenu-arrow {
    transform: translateY(-50%) rotate(90deg);
}

.submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: #1a252f;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
}

.submenu.active {
    max-height: 300px;
}

.submenu li {
    border-bottom: none;
}

.submenu a {
    padding: 12px 20px 12px 50px !important;
    font-size: 14px;
    color: #95a5a6;
    border-left: none !important;
    position: relative;
}

.submenu a:before {
    content: "├─";
    position: absolute;
    left: 30px;
    color: #7f8c8d;
    font-size: 12px;
}

.submenu li:last-child a:before {
    content: "└─";
}

.submenu a:hover,
.submenu a.active {
    background-color: #2c3e50;
    color: #3498db;
    border-left: 3px solid #3498db !important;
}

/* 刀具信息页面专用样式 */
.tool-filter-form {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.tool-summary {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.summary-card h4 {
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

.summary-value {
    font-size: 28px;
    font-weight: 700;
    display: block;
}

/* 数据表格样式 */
.data-table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

.data-table tr:nth-child(even) {
    background-color: #fbfbfb;
}

.data-table tr:nth-child(even):hover {
    background-color: #f0f0f0;
}

/* 导出操作按钮区域 */
.export-actions {
    text-align: center;
    padding: 20px;
    background: white;
    border-top: 1px solid #eee;
}

.btn-success {
    background-color: #27ae60;
    color: white;
}

.btn-success:hover {
    background-color: #229954;
}

/* 无数据提示 */
.no-data {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
    font-size: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 筛选表单增强 */
.form-row .form-group:last-child {
    display: flex;
    gap: 10px;
    align-items: end;
}

.form-row .form-group:last-child .btn {
    white-space: nowrap;
}

/* 响应式设计 - 刀具信息页面 */
@media (max-width: 1024px) {
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .form-row .form-group:last-child {
        flex-direction: column;
        align-items: stretch;
    }
    
    .data-table-container {
        overflow-x: auto;
    }
    
    .data-table {
        min-width: 600px;
    }
    
    .tool-filter-form .form-row {
        flex-direction: column;
        gap: 15px;
    }
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 20px;
    color: #7f8c8d;
}

.loading:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== 订单管理样式 ==================== */

/* 通用表单样式 */
.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    align-items: flex-start;
}

.form-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
    min-width: 300px;
    position: relative;
    gap: 10px;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    text-align: right;
    display: block;
    line-height: 1.4;
    min-width: 80px;
    max-width: 120px;
    flex-shrink: 0;
    white-space: nowrap;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fff;
    flex: 1;
    box-sizing: border-box;
    font-family: inherit;
    min-width: 0;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 现代化订单表单样式 */
.order-form-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.modern-order-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 表单卡片样式 */
.form-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.form-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    border-bottom: none;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h3 i {
    font-size: 20px;
    opacity: 0.9;
}

.card-body {
    padding: 24px;
}

/* 表单网格布局 */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    align-items: start;
}

/* 表单字段样式 */
.form-field {
    position: relative;
    display: flex;
    flex-direction: column;
}

.form-field.full-width {
    grid-column: 1 / -1;
}

.form-field label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.form-field label.required::after {
    content: "*";
    color: #e74c3c;
    font-weight: bold;
    margin-left: 4px;
}

.form-field input,
.form-field select,
.form-field textarea {
    padding: 14px 16px 14px 45px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fff;
    font-family: inherit;
    position: relative;
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.form-field input[readonly] {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    cursor: not-allowed;
}

.form-field textarea {
    resize: vertical;
    min-height: 100px;
    line-height: 1.5;
}

/* 字段图标 */
.field-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 16px;
    pointer-events: none;
    z-index: 1;
    margin-top: 14px; /* 调整图标位置以配合label */
}

.form-field.full-width .field-icon {
    margin-top: 14px;
}

/* 选择框特殊样式 */
.form-field select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 16px center;
    background-size: 16px;
    padding-right: 45px;
}

/* 占位符样式 */
.form-field input::placeholder,
.form-field textarea::placeholder {
    color: #a0aec0;
    font-style: italic;
}

/* 节标题样式增强 */
.section-header {
    text-align: center;
    margin-bottom: 32px;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.section-header h2 i {
    color: #667eea;
    font-size: 32px;
}

.section-subtitle {
    color: #6c757d;
    font-size: 16px;
    margin: 0;
    font-weight: 400;
}

/* 查询表单样式 */
.query-form {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.query-form .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    align-items: flex-start;
}

.query-form .form-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
    min-width: 280px;
    max-width: 400px;
    gap: 10px;
}

.query-form .form-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    text-align: right;
    min-width: 70px;
    max-width: 100px;
    flex-shrink: 0;
    white-space: nowrap;
}

.query-form .form-group label:after {
    content: "：";
    color: #2c3e50;
}

.query-form .form-group input,
.query-form .form-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    flex: 1;
    min-width: 0;
}

.query-form .form-group:last-child {
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
    min-width: auto;
    justify-content: flex-start;
}

.query-form .form-group:last-child .btn {
    margin-bottom: 0;
    white-space: nowrap;
    padding: 8px 16px;
    font-size: 14px;
}

/* 订单状态样式 */
.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-confirmed {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-production {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #99d6ff;
}

.status-completed {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 表格操作按钮 */
.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    margin-right: 5px;
}

/* 现代化表单操作按钮 */
.form-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 40px;
    padding: 32px 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.form-actions .btn {
    min-width: 140px;
    padding: 14px 28px;
    font-weight: 600;
    font-size: 14px;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.form-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.form-actions .btn:hover::before {
    left: 100%;
}

.form-actions .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.form-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.form-actions .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.form-actions .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
}

.form-actions .btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.form-actions .btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
}

.form-actions .btn:active {
    transform: translateY(0);
}

.form-actions .btn i {
    font-size: 16px;
}

/* 现代化响应式设计 */
@media (max-width: 1200px) {
    .order-form-wrapper {
        padding: 0 16px;
    }

    .form-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
    }
}

@media (max-width: 768px) {
    .order-form-wrapper {
        padding: 0 12px;
    }

    .modern-order-form {
        gap: 20px;
    }

    .form-card {
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .card-header {
        padding: 16px 20px;
    }

    .card-header h3 {
        font-size: 16px;
    }

    .card-body {
        padding: 20px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .form-field input,
    .form-field select,
    .form-field textarea {
        padding: 12px 14px 12px 40px;
        border-radius: 10px;
    }

    .field-icon {
        left: 14px;
        font-size: 14px;
    }

    .form-actions {
        flex-direction: column;
        align-items: center;
        gap: 12px;
        padding: 24px 20px;
        margin-top: 24px;
    }

    .form-actions .btn {
        width: 100%;
        max-width: 280px;
        min-width: auto;
        padding: 12px 24px;
    }

    .section-header h2 {
        font-size: 24px;
        flex-direction: column;
        gap: 8px;
    }

    .section-header h2 i {
        font-size: 28px;
    }

    .section-subtitle {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .order-form-wrapper {
        padding: 0 8px;
    }

    .card-header {
        padding: 12px 16px;
    }

    .card-body {
        padding: 16px;
    }

    .form-field input,
    .form-field select,
    .form-field textarea {
        padding: 10px 12px 10px 36px;
        font-size: 13px;
    }

    .field-icon {
        left: 12px;
        font-size: 13px;
    }

    .form-actions {
        padding: 20px 16px;
    }

    .form-actions .btn {
        padding: 10px 20px;
        font-size: 13px;
    }
}

    .query-form .form-group:last-child {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .form-actions {
        flex-direction: column;
        align-items: center;
    }

    .form-actions .btn {
        width: 100%;
        max-width: 200px;
    }
}

/* 订单详情模态框样式 */
.order-detail-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.order-detail-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.order-detail-header {
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.order-detail-body {
    padding: 30px;
}

.order-detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.order-detail-item {
    display: flex;
    flex-direction: column;
}

.order-detail-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.order-detail-value {
    color: #2c3e50;
    font-size: 14px;
    font-weight: 500;
}

/* 订单预览模态框样式 */
.order-preview-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.order-preview-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    margin: 2% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.order-preview-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 32px;
    border-radius: 20px 20px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-preview-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.close-preview {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.close-preview:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.order-preview-body {
    padding: 32px;
    max-height: 60vh;
    overflow-y: auto;
}

.preview-section {
    margin-bottom: 32px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.preview-section h4 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 12px;
    border-bottom: 2px solid #667eea;
}

.preview-section h4 i {
    color: #667eea;
    font-size: 18px;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.preview-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.preview-item.full-width {
    grid-column: 1 / -1;
}

.preview-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.preview-value {
    color: #2c3e50;
    font-size: 15px;
    font-weight: 500;
    padding: 8px 12px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    border-left: 3px solid #667eea;
    min-height: 20px;
    word-wrap: break-word;
}

.order-preview-footer {
    padding: 24px 32px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 0 0 20px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.order-preview-footer .btn {
    min-width: 120px;
    padding: 12px 24px;
    font-weight: 600;
    border-radius: 10px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* 预览模态框响应式设计 */
@media (max-width: 768px) {
    .order-preview-content {
        width: 95%;
        margin: 5% auto;
        border-radius: 16px;
    }

    .order-preview-header {
        padding: 20px 24px;
        border-radius: 16px 16px 0 0;
    }

    .order-preview-header h3 {
        font-size: 18px;
    }

    .order-preview-body {
        padding: 24px 20px;
    }

    .preview-section {
        padding: 20px;
        margin-bottom: 24px;
        border-radius: 12px;
    }

    .preview-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .order-preview-footer {
        padding: 20px 24px;
        flex-direction: column;
        gap: 12px;
    }

    .order-preview-footer .btn {
        width: 100%;
    }
}
