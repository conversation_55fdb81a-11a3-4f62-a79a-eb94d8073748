#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc
import sys

# 数据库连接配置
server = 'localhost'
database = 'DeviceDataSource'
username = 'sa'
password = '123456'

def main():
    try:
        # 连接数据库
        conn_str = f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        print("✅ 数据库连接成功")
        
        # 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) FROM sys.tables WHERE name = 'devinfotable'
        """)
        
        if cursor.fetchone()[0] > 0:
            print('✅ devinfotable 表存在')
            
            # 检查并添加 devtype 字段
            cursor.execute("""
                SELECT COUNT(*) FROM sys.columns 
                WHERE object_id = OBJECT_ID('devinfotable') AND name = 'devtype'        
            """)
            
            if cursor.fetchone()[0] == 0:
                cursor.execute('ALTER TABLE devinfotable ADD devtype nvarchar(50) NULL')
                print('✅ 已添加 devtype 字段')
            else:
                print('ℹ️  devtype 字段已存在')
            
            # 检查并添加 workspace 字段
            cursor.execute("""
                SELECT COUNT(*) FROM sys.columns
                WHERE object_id = OBJECT_ID('devinfotable') AND name = 'workspace'      
            """)
            
            if cursor.fetchone()[0] == 0:
                cursor.execute('ALTER TABLE devinfotable ADD workspace nvarchar(100) NULL')
                print('✅ 已添加 workspace 字段')
            else:
                print('ℹ️  workspace 字段已存在')
            
            # 更新现有数据
            cursor.execute("""
                UPDATE devinfotable SET 
                    devtype = CASE
                        WHEN devno LIKE 'WJ%' THEN N'五轴'
                        WHEN devno LIKE 'SC%' THEN N'数控'
                        WHEN devno LIKE 'JS%' THEN N'加工中心'
                        ELSE N'通用设备'
                    END,
                    workspace = CASE
                        WHEN devno IN ('WJ01', 'WJ02', 'WJ03', 'WJ04') THEN N'A区'      
                        WHEN devno IN ('SC01', 'SC02', 'SC03', 'SC04') THEN N'B区'      
                        WHEN devno IN ('SC05', 'SC06', 'SC07', 'SC08') THEN N'C区'      
                        ELSE N'未分配'
                    END
                WHERE devtype IS NULL OR workspace IS NULL
            """)
            
            conn.commit()
            print('✅ 设备类型和生产位置信息已更新')
            
            # 查看更新结果
            cursor.execute('SELECT devno, devtype, workspace FROM devinfotable')
            rows = cursor.fetchall()
            print('\n📋 当前设备信息:')
            for row in rows:
                print(f'设备: {row[0]}, 类型: {row[1]}, 位置: {row[2]}')
                
        else:
            print('❌ devinfotable 表不存在')
            return False
        
        conn.close()
        print('\n🎉 数据库字段更新完成!')
        return True
        
    except Exception as e:
        print(f'❌ 数据库操作失败: {str(e)}')
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
