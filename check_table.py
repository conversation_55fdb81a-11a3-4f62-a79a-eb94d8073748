import pyodbc

# 使用与app.py相同的连接方式
DRIVERS = [
    '{ODBC Driver 17 for SQL Server}',
    '{ODBC Driver 13 for SQL Server}',
    '{SQL Server Native Client 11.0}',
    '{SQL Server}',
]

def get_db_connection():
    """获取数据库连接 - Windows认证"""
    for driver in DRIVERS:
        try:
            connection_string = f"""
                DRIVER={driver};
                SERVER=localhost;
                DATABASE=DeviceDataSource;
                Trusted_Connection=yes;
                TrustServerCertificate=yes;
            """
            connection = pyodbc.connect(connection_string)
            return connection
        except Exception as e:
            print(f"尝试驱动 {driver} 失败: {e}")
            continue
    return None

try:
    conn = get_db_connection()
    if not conn:
        print("无法连接数据库")
        exit(1)
    cursor = conn.cursor()

    print('连接成功，查看表结构...')

    # 查看表的实际列结构
    cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'devinfotable' ORDER BY ORDINAL_POSITION")

    print('devinfotable 表的列名:')
    columns = []
    for row in cursor.fetchall():
        columns.append(row[0])
        print(f'- {row[0]}')

    # 检查是否有 devtype 和 workspace 字段
    if 'devtype' in columns:
        print('✅ devtype 字段存在')
    else:
        print('❌ devtype 字段不存在')

    if 'workspace' in columns:
        print('✅ workspace 字段存在')
    else:
        print('❌ workspace 字段不存在')

    conn.close()

except Exception as e:
    print(f'错误: {e}')
